<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="<%= csrfToken %>">
    <!-- CRITICAL FIX: Prevent browser caching for dynamic payroll data -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>Employee Profile</title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- External Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/date-fns/2.29.3/date-fns.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/employeeProfile.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-employee-profile.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />

    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:opsz,wght@6..12,200&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Familjen+Grotesk&display=swap" rel="stylesheet" />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
      /* Professional Employee Profile Styles - PandaPayroll Design System */

      /* Enterprise Tag System - Conservative Business Styling */
      .tag {
        font-size: 0.6875rem;
        padding: 0.125rem 0.375rem;
        border-radius: 2px;
        font-weight: 500;
        white-space: nowrap;
        margin-left: 0.5rem;
        border: 1px solid #d1d5db;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        background-color: #f9fafb;
        color: #374151;
      }

      .tag.non-taxable {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.taxable {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.age-tag {
        background-color: #f9fafb;
        color: #6b7280;
        border-color: #d1d5db;
        font-size: 0.625rem;
        text-transform: none;
        letter-spacing: normal;
      }

      .tag.prorated {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.tax-deductible {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.employer {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.info {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.tax-exempt {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.tax-benefit {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.tax-directive {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      .tag.members {
        background-color: #f9fafb;
        color: #374151;
        border-color: #d1d5db;
      }

      /* Calculator Section Improvements */
      .calc-page {
        display: none;
      }

      .calc-page[data-page="1"] {
        display: block;
      }

      .calc-group {
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
      }

      .calc-group h3 {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        color: #1e293b;
        font-size: 1rem;
        font-weight: 600;
      }

      .calc-group h3 i {
        color: #6366f1;
        font-size: 1.125rem;
      }

      .calc-group.allowances {
        margin-top: 1rem;
        background: #ffffff;
      }

      .allowance-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
      }

      .allowance-breakdown {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 0.25rem;
      }

      .row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0.75rem 0;
        border-bottom: 1px solid #e2e8f0;
      }

      .row:last-child {
        border-bottom: none;
      }

      .row.total {
        margin-top: 0.5rem;
        padding-top: 1rem;
        border-top: 2px solid #e2e8f0;
        border-bottom: none;
        font-weight: 600;
        background: #f8fafc;
        margin-left: -1rem;
        margin-right: -1rem;
        padding-left: 1rem;
        padding-right: 1rem;
      }

      .row.sub-item {
        padding-left: 1rem;
        font-size: 0.875rem;
        color: #64748b;
        background: #ffffff;
        margin-left: -1rem;
        margin-right: -1rem;
        padding-right: 1rem;
      }

      .row.sub-item .allowance-info {
        display: flex;
        align-items: center;
        flex-direction: row;
        gap: 0.5rem;
      }

      .calc-group.summary .row {
        padding: 1rem 0;
        border-bottom: 1px solid #e2e8f0;
      }

      .calc-group.summary .row.total {
        border-bottom: none;
        border-top: 2px solid #6366f1;
        font-size: 1.1em;
        color: #6366f1;
      }

      .salary-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        align-items: flex-end;
      }

      .proration-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #64748b;
      }

      /* Enterprise Action Buttons */
      .downloadPayslip {
        margin: 0;
      }

      .downloadPayslip button,
      #finaliseButton {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background-color: #6366f1;
        color: white;
        border: 1px solid #6366f1;
        border-radius: 2px;
        font-family: 'Inter', sans-serif;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.1s ease;
        box-shadow: none;
        width: 100%;
        text-align: center;
        white-space: nowrap;
      }

      .downloadPayslip button:hover,
      #finaliseButton:hover:not([disabled]) {
        background-color: #4f46e5;
        border-color: #4f46e5;
      }

      .downloadPayslip button:active,
      #finaliseButton:active:not([disabled]) {
        background-color: #4338ca;
        border-color: #4338ca;
      }

      #finaliseButton[disabled] {
        background-color: #9ca3af;
        border-color: #9ca3af;
        cursor: not-allowed;
        opacity: 0.7;
      }

      /* Unfinalize button styling */
      #finaliseButton.unfinalize-button {
        background-color: #f59e0b;
        border-color: #f59e0b;
        color: white;
      }

      #finaliseButton.unfinalize-button:hover:not([disabled]) {
        background-color: #d97706;
        border-color: #d97706;
      }

      #finaliseButton.unfinalize-button:active:not([disabled]) {
        background-color: #b45309;
        border-color: #b45309;
      }

      /* Finalized period state management */
      .period-finalized .input-link,
      .period-finalized .placeholder-link,
      .period-finalized .add-button {
        pointer-events: none;
        opacity: 0.6;
        cursor: not-allowed;
        position: relative;
      }

      .period-finalized .input-link::after,
      .period-finalized .placeholder-link::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(156, 163, 175, 0.3);
        border-radius: inherit;
        z-index: 1;
      }

      .period-finalized .add-button {
        background-color: #e5e7eb;
        color: #9ca3af;
        border-color: #d1d5db;
      }

      .period-finalized .add-button:hover {
        background-color: #e5e7eb;
        color: #9ca3af;
        border-color: #d1d5db;
      }

      /* Period status indicator */
      .period-status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        margin-left: 0.5rem;
      }

      .period-status-indicator.finalized {
        background-color: #fef3c7;
        color: #92400e;
        border: 1px solid #fbbf24;
      }

      .period-status-indicator.unfinalized {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
      }



      /* Toast Notification System */
      .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
      }

      .toast {
        background: white;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        margin-bottom: 10px;
        padding: 1rem;
        border-left: 4px solid;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
      }

      .toast.show {
        opacity: 1;
        transform: translateX(0);
      }

      .toast.success {
        border-left-color: #10b981;
      }

      .toast.error {
        border-left-color: #ef4444;
      }

      .toast.warning {
        border-left-color: #f59e0b;
      }

      .toast.info {
        border-left-color: #3b82f6;
      }

      .toast-icon {
        font-size: 1.25rem;
        margin-top: 0.125rem;
      }

      .toast.success .toast-icon {
        color: #10b981;
      }

      .toast.error .toast-icon {
        color: #ef4444;
      }

      .toast.warning .toast-icon {
        color: #f59e0b;
      }

      .toast.info .toast-icon {
        color: #3b82f6;
      }

      .toast-content {
        flex: 1;
      }

      .toast-title {
        font-weight: 600;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
        color: #1f2937;
      }

      .toast-message {
        font-size: 0.8125rem;
        color: #6b7280;
        line-height: 1.4;
      }

      .toast-close {
        background: none;
        border: none;
        color: #9ca3af;
        cursor: pointer;
        font-size: 1rem;
        padding: 0;
        margin-left: 0.5rem;
      }

      .toast-close:hover {
        color: #6b7280;
      }

      /* Confirmation Dialog System */
      .confirmation-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .confirmation-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .confirmation-dialog {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        transform: scale(0.9);
        transition: transform 0.3s ease;
      }

      .confirmation-overlay.show .confirmation-dialog {
        transform: scale(1);
      }

      .confirmation-header {
        padding: 1.5rem 1.5rem 1rem;
        border-bottom: 1px solid #e5e7eb;
      }

      .confirmation-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .confirmation-body {
        padding: 1.5rem;
      }

      .confirmation-message {
        color: #6b7280;
        line-height: 1.6;
        margin-bottom: 1rem;
      }

      .confirmation-details {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
      }

      .confirmation-details h4 {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
      }

      .confirmation-details ul {
        margin: 0;
        padding-left: 1.25rem;
        color: #6b7280;
        font-size: 0.8125rem;
      }

      .confirmation-actions {
        padding: 1rem 1.5rem 1.5rem;
        border-top: 1px solid #e5e7eb;
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;
      }

      .confirmation-btn {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        border: 1px solid;
        transition: all 0.2s ease;
      }

      .confirmation-btn.cancel {
        background: white;
        color: #6b7280;
        border-color: #d1d5db;
      }

      .confirmation-btn.cancel:hover {
        background: #f9fafb;
        color: #374151;
      }

      .confirmation-btn.confirm {
        background: #ef4444;
        color: white;
        border-color: #ef4444;
      }

      .confirmation-btn.confirm:hover {
        background: #dc2626;
        border-color: #dc2626;
      }

      .confirmation-btn.warning {
        background: #f59e0b;
        color: white;
        border-color: #f59e0b;
      }

      .confirmation-btn.warning:hover {
        background: #d97706;
        border-color: #d97706;
      }

      /* Payroll Actions Card - Integrated Design */
      .payroll-actions-card {
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.2s ease;
      }

      .payroll-actions-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-1px);
      }

      .payroll-actions-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e2e8f0;
      }

      .payroll-actions-header h2 {
        color: #1e293b;
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0;
        font-family: 'Inter', sans-serif;
      }

      .payroll-actions-header i {
        color: #6366f1;
        font-size: 1.25rem;
      }

      .payroll-actions-content {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
      }

      .payroll-actions-content form {
        margin: 0;
        width: 100%;
      }

      .payroll-actions-content .downloadPayslip {
        width: 100%;
      }

      /* Responsive Payroll Actions Card */
      @media (max-width: 768px) {
        .payroll-actions-card {
          margin-bottom: 1rem;
          border-radius: 8px;
          padding: 1rem;
        }

        .payroll-actions-header {
          margin-bottom: 1rem;
          padding-bottom: 0.75rem;
        }

        .payroll-actions-header h2 {
          font-size: 1.125rem;
        }

        .payroll-actions-content {
          gap: 0.5rem;
        }

        .downloadPayslip button,
        #finaliseButton {
          font-size: 0.875rem;
          padding: 0.75rem 1rem;
        }
      }

      @media (max-width: 480px) {
        .payroll-actions-card {
          padding: 0.75rem;
        }

        .payroll-actions-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.5rem;
        }

        .payroll-actions-header h2 {
          font-size: 1rem;
        }

        .downloadPayslip button,
        #finaliseButton {
          font-size: 0.8125rem;
          padding: 0.75rem;
        }
      }
    </style>
</head>
<body data-company-code="<%= companyCode %>" data-termination-date="<%= employee.lastDayOfService ? moment.utc(employee.lastDayOfService).format('YYYY-MM-DD') : '' %>">
    <%- include('partials/header') %>
    <nav><%- include('partials/sidebar') %></nav>

    <main class="main-container">
        <!-- Professional Employee Profile Header -->
        <section class="title-section" style="margin-top: 6.5rem;">
          <div class="profile-header">
            <div class="profile-avatar" title="Employee initials">
              <% if (employee.profileImage) { %>
                <img src="<%= employee.profileImage %>" alt="<%= employee.firstName %>'s profile"
                     onerror="this.onerror=null; this.src=null; this.classList.add('fallback'); this.innerHTML='<%= employee.firstName[0] %><%= employee.lastName[0] %>'">
              <% } else { %>
                <div class="avatar-initials">
                  <%= employee.firstName[0] %><%= employee.lastName[0] %>
                </div>
              <% } %>
            </div>

            <div class="profile-info">
              <div class="profile-name-section">
                <h1 class="profile-name">
                  <%= employee.firstName %> <%= employee.lastName %>
                  <div class="employee-status"
                       data-status="<%= employee.status || 'Active' %>"
                       title="Employee current status">
                    <i class="ph ph-circle-fill"></i>
                    <span><%= employee.status || 'Active' %></span>
                  </div>
                </h1>
                <div class="profile-title" title="Employee job title">
                  <i class="ph ph-briefcase"></i>
                  <%= employee.jobTitle || 'Employee' %>
                </div>
              </div>
            </div>
          </div>

          <div class="quick-info">
            <div class="info-item" title="Employee ID">
              <div class="info-icon">
                <i class="ph ph-identification-card"></i>
              </div>
              <div class="info-text">
                <span class="info-label">Employee ID</span>
                <span class="info-value"><%= employee.companyEmployeeNumber || 'Not Assigned' %></span>
              </div>
            </div>

            <div class="info-item" title="Pay Frequency">
              <div class="info-icon">
                <i class="ph ph-calendar"></i>
              </div>
              <div class="info-text">
                <span class="info-label">Pay Frequency</span>
                <span class="info-value"><%= payFrequency || 'Not Set' %></span>
              </div>
            </div>

            <div class="info-item" title="Department">
              <div class="info-icon">
                <i class="ph ph-buildings"></i>
              </div>
              <div class="info-text">
                <span class="info-label">Department</span>
                <span class="info-value"><%= employee.department || 'Not Assigned' %></span>
              </div>
            </div>
            <%
              // Calculate employee age
              const calculateAge = (dob) => {
                const birthDate = new Date(dob);
                const today = new Date();
                let age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                  age--;
                }
                return age;
              };

              const employeeAge = employee.dob ? calculateAge(employee.dob) : 'N/A';
            %>
            <% if (dobFormatted) { %>
                <div class="info-item" title="Date of Birth">
                  <div class="info-icon">
                    <i class="ph ph-cake"></i>
                  </div>
                  <div class="info-text">
                    <span class="info-label">Date of Birth</span>
                    <span class="info-value">
                        <%= dobFormatted %>
                        <% if (employeeAge && employeeAge > 0) { %>
                            <span class="tag age-tag"><%= employeeAge %> years</span>
                        <% } %>
                    </span>
                  </div>
                </div>
            <% } %>
          </div>
        </section>

  <!-- Professional Action Navigation -->
<div id="tabs-container">
  <section id="tabs-section">
    <!-- Payroll Tab -->
    <button class="tab-button active" onclick="showTab('payroll')">
      <i class="ph ph-calculator"></i>
      Payroll
    </button>

    <!-- Edit Info Dropdown -->
    <div class="dropdown">
      <button class="tab-button" onclick="toggleDropdown(event)">
        <i class="ph ph-pencil-simple"></i>
        Edit Info
        <i class="ph ph-caret-down"></i>
      </button>
      <div id="dropdownContent" class="dropdown-content">
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/edit">
          <i class="ph ph-user"></i>
          Basic Info
        </a>
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/classifications">
          <i class="ph ph-tag"></i>
          Classifications
        </a>
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/defineRFI">
          <i class="ph ph-file-text"></i>
          Define RFI
        </a>
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/regularHours">
          <i class="ph ph-clock"></i>
          Regular Hours
        </a>
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/eti">
          <i class="ph ph-chart-line"></i>
          ETI
        </a>
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity">
          <i class="ph ph-star"></i>
          Skills Equity
        </a>
      </div>
    </div>

    <!-- Leave Tab -->
    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeManagement/employee/<%= employee._id %>/leave'">
      <i class="ph ph-calendar-blank"></i>
      Leave
    </button>

    <!-- End Service Tab -->
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service"
       class="tab-button warning"
       role="button">
      <i class="ph ph-sign-out"></i>
      <%= (employee.status === 'Inactive' || employee.status === 'Serving Notice') ? 'Manage End of Service' : 'End Service' %>
    </a>

    <!-- Delete Employee Tab -->
    <button class="tab-button danger" onclick="handleDeleteEmployee('<%= employee._id %>')">
      <i class="ph ph-trash"></i>
      Delete Employee
    </button>
  </section>
</div>
       <!-- Main Content Grid -->
<section id="content-section" class="<%= currentPeriod && currentPeriod.isFinalized ? 'period-finalized' : '' %>">

  <!-- Regular Inputs Card -->
  <div class="input-card <%= currentPeriod?.isFinalized ? 'finalized-period' : '' %>">
    <div class="header">
        <h2>Regular Inputs</h2>
        <% if (!currentPeriod?.isFinalized) { %>
            <button class="add-button">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularInputs">
                    <i class="ph ph-plus"></i> Add
                </a>
            </button>
        <% } %>
    </div>
    <div class="regular-items">
        <div class="item">
            <% if ((currentPeriod?.basicSalary === undefined || currentPeriod?.basicSalary === null) && !payroll?.basicSalary) { %>
                <% if (currentPeriod?.isFinalized) { %>
                    <div class="input-link finalized-input">
                        <div class="item-header">
                            <i class="ph ph-currency-circle-dollar"></i>
                            Basic Salary
                        </div>
                        <div class="item-value empty">Not configured</div>
                    </div>
                <% } else { %>
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/addBasicSalary<%= currentPeriod && currentPeriod.endDate ? '?selectedMonth=' + encodeURIComponent(currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD')) : '' %>" class="input-link">
                        <div class="item-header">
                            <i class="ph ph-currency-circle-dollar"></i>
                            Basic Salary
                        </div>
                        <div class="item-value empty">Click to add</div>
                    </a>
                <% } %>
            <% } else { %>
                <div class="item-header">
                    <i class="ph ph-currency-circle-dollar"></i>
                    <% if (currentPeriod?.isFinalized) { %>
                        <span class="finalized-link">Basic Salary</span>
                    <% } else { %>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/addBasicSalary<%= currentPeriod && currentPeriod.endDate ? '?selectedMonth=' + encodeURIComponent(currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD')) : '' %>">Basic Salary</a>
                    <% } %>
                </div>
                <div class="item-value">
                    <!-- CRITICAL FIX: Use only the historical basicSalary value, no fallbacks to current salary -->
                    R <%= Number(basicSalary || 0).toFixed(2) %>
                </div>
            <% } %>
        </div>
        <% 
        const hasTravelAllowance = payroll?.travelAllowance?.fixedAllowanceAmount > 0 || 
                                 payroll?.travelAllowance?.businessKilometers > 0 ||
                                 payroll?.travelAllowance?.petrolCardValue > 0;
        
        if (hasTravelAllowance) { 
          const totalAllowance = payroll.travelAllowance.calculationDetails?.totalAllowance || 0;
          const taxableAmount = payroll.travelAllowance.calculationDetails?.taxableAmount || 0;
          const nonTaxableAmount = payroll.travelAllowance.calculationDetails?.nonTaxableAmount || 0;
      %>
        <div class="item">

                <div class="item-header">
                    <i class="ph ph-car"></i>
                    <% if (currentPeriod?.isFinalized) { %>
                        <span class="finalized-link">Travel Allowance</span>
                    <% } else { %>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/travel-allowance">Travel Allowance</a>
                    <% } %>
                </div>
                <div class="item-value">
                    R <%= Number(totalAllowance).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                    <% if (taxableAmount > 0) { %>
                        <span class="tag taxable">Taxable: R <%= Number(taxableAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                    <% } %>
                    <% if (nonTaxableAmount > 0) { %>
                        <span class="tag non-taxable">Non-Taxable: R <%= Number(nonTaxableAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                    <% } %>
                </div>
        </div>
        <% } %>

        <% if (payroll?.accommodationBenefit > 0) { %>
        <div class="item">
                <div class="item-header">
                    <i class="ph ph-house"></i>
                    <% if (currentPeriod?.isFinalized) { %>
                        <span class="finalized-link">Accommodation Benefit</span>
                    <% } else { %>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/accommodation-benefit">Accommodation Benefit</a>
                    <% } %>
                </div>
                <div class="item-value">
                    R <%= Number(payroll.accommodationBenefit).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                    <span class="tag taxable">Taxable</span>
                </div>
        </div>
        <% } %>


        <% if (payroll?.companyCarUnderOperatingLease?.amount > 0) { %>
          <div class="item">
              <div class="item-header">
                  <i class="ph ph-car-profile"></i>
                  <% if (currentPeriod?.isFinalized) { %>
                      <span class="finalized-link">Company Car Under Operating Lease</span>
                  <% } else { %>
                      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/company-car-lease">Company Car Under Operating Lease</a>
                  <% } %>
              </div>
              <div class="item-value">
                  R <%= Number(payroll.companyCarUnderOperatingLease.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                  <span class="tag taxable">Taxable</span>
              </div>
          </div>
      <% } %>

        <% if (payroll?.commissionEnabled) { %>
        <div class="item">
            <% if (payroll?.commission > 0) { %>
                <div class="item-header">
                    <i class="ph ph-trending-up"></i>
                    <% if (currentPeriod?.isFinalized) { %>
                        <span class="finalized-link">Commission</span>
                    <% } else { %>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/commission">Commission</a>
                    <% } %>
                </div>
                <div class="item-value">
                    R <%= Number(payroll.commission).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                    <span class="tag taxable">Taxable</span>
                </div>
            <% } else { %>
                <% if (currentPeriod?.isFinalized) { %>
                    <div class="input-link finalized-input">
                        <div class="item-header">
                            <i class="ph ph-trending-up"></i>
                            Commission
                        </div>
                        <div class="item-value empty">Not configured</div>
                    </div>
                <% } else { %>
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/commission" class="input-link">
                        <div class="item-header">
                            <i class="ph ph-trending-up"></i>
                            Commission
                        </div>
                        <div class="item-value empty">Click to add</div>
                    </a>
                <% } %>
            <% } %>
        </div>
        <% } %>

        <% if (payroll?.companyCar?.deemedValue > 0) { %>
          <div class="item">
              <div class="item-header">
                  <i class="ph ph-car"></i>
                  <% if (currentPeriod?.isFinalized) { %>
                      <span class="finalized-link">Company Car</span>
                  <% } else { %>
                      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/company-car">Company Car</a>
                  <% } %>
              </div>
              <div class="item-value">
                  R <%= Number(payroll.companyCar.deemedValue).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                  <span class="tag taxable">Taxable</span>
                  <% if (payroll.companyCar.includesMaintenancePlan) { %>
                      <span class="tag info">Includes Maintenance Plan</span>
                  <% } %>
              </div>
          </div>
      <% } %>

      <% if (payroll?.lossOfIncome > 0) { %>
        <div class="item">
                <% if (currentPeriod?.isFinalized) { %>
                    <div class="input-link finalized-input">
                        <div class="item-header">
                            <i class="ph ph-heart-break"></i>
                            Loss of Income
                            <% if (payroll?.data?.get('lossOfIncomeDate')) { %>
                                <span class="date-tag">
                                    <%= new Date(payroll.data.get('lossOfIncomeDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
                                </span>
                            <% } %>
                        </div>
                        <div class="item-value">
                            R <%= Number(payroll.lossOfIncome).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                            <span class="tag non-taxable">Non-Taxable</span>
                        </div>
                    </div>
                <% } else { %>
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/loss-of-income" class="input-link">
                        <div class="item-header">
                            <i class="ph ph-heart-break"></i>
                            Loss of Income
                            <% if (payroll?.data?.get('lossOfIncomeDate')) { %>
                                <span class="date-tag">
                                    <%= new Date(payroll.data.get('lossOfIncomeDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
                                </span>
                            <% } %>
                        </div>
                        <div class="item-value">
                            R <%= Number(payroll.lossOfIncome).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                            <span class="tag non-taxable">Non-Taxable</span>
                        </div>
                    </a>
                <% } %>
        </div>
        <% } %>

        <% if (payroll?.retirementAnnuityFund?.employeeContribution > 0 || payroll?.retirementAnnuityFund?.employerContribution > 0) { %>
        <div class="item">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/retirement-annuity-fund" class="input-link">
                    <div class="item-header">
                        <i class="ph ph-piggy-bank"></i>
                        Retirement Annuity Fund
                        <% if (payroll?.data?.get('retirementAnnuityFundDate')) { %>
                            <span class="date-tag">
                                <%= new Date(payroll.data.get('retirementAnnuityFundDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
                            </span>
                        <% } %>
                    </div>
                    <div class="item-value">
                        R <%= Number(payroll.retirementAnnuityFund.employeeContribution).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                        <span class="tag tax-deductible">Tax Deductible</span>
                        <% if (payroll.retirementAnnuityFund.employerContribution > 0) { %>
                            <span class="tag employer">Employer: R <%= Number(payroll.retirementAnnuityFund.employerContribution).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                        <% } %>
                    </div>
                </a>
        </div>
        <% } %>

        <% if (payroll?.maintenanceOrder > 0) { %>
        <div class="item">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/maintenance-order" class="input-link">
                    <div class="item-header">
                        <i class="ph ph-gavel"></i>
                        Maintenance Order
                        <% if (payroll?.data?.get('maintenanceOrderDate')) { %>
                            <span class="date-tag">
                                <%= new Date(payroll.data.get('maintenanceOrderDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
                            </span>
                        <% } %>
                    </div>
                    <div class="item-value">
                        R <%= Number(payroll.maintenanceOrder).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                        <span class="tag non-taxable">Non-Taxable</span>
                    </div>
                </a>
        </div>
        <% } %>

        <!-- Deductions-->

        <% if (payroll?.pensionFund?.fixedContributionEmployee > 0 || payroll?.pensionFund?.rfiEmployee > 0) { %>
          <div class="item">
              <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/pension-fund" class="input-link">
                  <div class="item-header">
                      <i class="ph ph-bank"></i>
                      Pension Fund
                  </div>
                  <div class="item-value">
                      <% if (payroll.pensionFund.contributionCalculation === 'fixedAmount') { %>
                          R <%= Number(payroll.pensionFund.fixedContributionEmployee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                      <% } else { %>
                          <%= payroll.pensionFund.rfiEmployee %>% of RFI
                      <% } %>
                      <span class="tag tax-deductible">Tax Deductible</span>
                  </div>
              </a>
          </div>
          <% } %>

          <% if (payroll?.medical?.medicalAid > 0) { %>
            <div class="item">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/medical-aid" class="input-link">
                    <div class="item-header">
                        <i class="ph ph-first-aid"></i>
                        Medical Aid
                        <% if (payroll?.data?.get('medicalAidDate')) { %>
                            <span class="date-tag">
                                <%= new Date(payroll.data.get('medicalAidDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
                            </span>
                        <% } %>
                    </div>
                    <div class="item-value">
                        R <%= Number(payroll.medical.medicalAid).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                        <% if (payroll.medical.employerContribution > 0) { %>
                            <span class="tag employer">Employer: R <%= Number(payroll.medical.employerContribution).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                        <% } %>
                        <span class="tag members"><%= payroll.medical.members %> <%= payroll.medical.members === 1 ? 'Member' : 'Members' %></span>
                        <span class="tag tax-deductible">Tax Deductible</span>
                    </div>
                </a>
            </div>
          <% } %>

          <% if (payroll?.retirementAnnuityFund?.employeeContribution > 0) { %>
              <div class="item">
                  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/retirement-annuity" class="input-link">
                      <div class="item-header">
                          <i class="ph ph-piggy-bank"></i>
                          Retirement Annuity
                      </div>
                      <div class="item-value">
                          R <%= Number(payroll.retirementAnnuityFund.employeeContribution).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                          <span class="tag tax-deductible">Tax Deductible</span>
                          <% if (payroll.retirementAnnuityFund.employerContribution > 0) { %>
                              <span class="tag employer">+ Employer</span>
                          <% } %>
                      </div>
                  </a>
              </div>
              <% } %>

              <% if (payroll?.maintenanceOrder?.amount > 0) { %>
                <div class="input-placeholder maintenance-order">
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/maintenance-order" class="placeholder-link">
                        <div class="placeholder-content">
                            <div class="placeholder-header">
                                <i class="ph ph-gavel"></i>
                                <span class="component-name">Maintenance Order</span>
                            </div>
                            <div class="placeholder-details">
                                <span class="amount">R <%= Number(payroll.maintenanceOrder.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                                <span class="tag non-taxable">Non-Taxable</span>
                            </div>
                        </div>
                    </a>
                </div>
                <% } %>

                <% if (payroll?.providentFund?.fixedContributionEmployee > 0 || payroll?.providentFund?.rfiEmployee > 0) { %>
                  <div class="item">
                      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/provident-fund" class="input-link">
                          <div class="item-header">
                              <i class="ph ph-bank"></i>
                              Provident Fund
                          </div>
                          <div class="item-value">
                              <% if (payroll.providentFund.contributionCalculation === 'fixedAmount') { %>
                                  R <%= Number(payroll.providentFund.fixedContributionEmployee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                              <% } else { %>
                                  <%= payroll.providentFund.rfiEmployee %>% of RFI
                              <% } %>
                              <span class="tag tax-deductible">Tax Deductible</span>
                              <% if (payroll.providentFund.employerContribution > 0) { %>
                                  <span class="tag employer">+ Employer</span>
                              <% } %>
                          </div>
                      </a>
                  </div>
                  <% } %>

                  <% if (payroll?.unionMembershipFee > 0) { %>
                    <div class="item">
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/union-membership-fee" class="input-link">
                            <div class="item-header">
                                <i class="ph ph-handshake"></i>
                                Union Membership Fee
                            </div>
                            <div class="item-value">
                                R <%= Number(payroll.unionMembershipFee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                                <span class="tag tax-deductible">Tax Deductible</span>
                            </div>
                        </a>
                    </div>
                    <% } %>

                    <% if (payroll?.incomeProtection?.amountDeductedFromEmployee > 0 || payroll?.incomeProtection?.amountPaidByEmployer > 0) { %>
                      <div class="item">
                          <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/income-protection" class="input-link">
                              <div class="item-header">
                                  <i class="ph ph-shield-plus"></i>
                                  Income Protection
                              </div>
                              <div class="item-value">
                                  R <%= Number(payroll.incomeProtection.amountDeductedFromEmployee + payroll.incomeProtection.amountPaidByEmployee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                                  <span class="tag tax-deductible">Tax Deductible</span>
                                  <% if (payroll.incomeProtection.amountPaidByEmployer > 0) { %>
                                      <span class="tag employer">+ Employer</span>
                                  <% } %>
                                  <% if (payroll.incomeProtection.employerOwnsPolicy) { %>
                                      <span class="tag info">Employer Policy</span>
                                  <% } %>
                              </div>
                          </a>
                      </div>
                      <% } %>


              <!-- Other-->

              <% if (payroll?.savings?.regularDeduction > 0) { %>
                <div class="item">
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/savings" class="input-link">
                        <div class="item-header">
                            <i class="ph ph-piggy-bank"></i>
                            Savings
                        </div>
                        <div class="item-value">
                            R <%= Number(payroll.savings.regularDeduction).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                            <span class="tag non-taxable">Non-Taxable</span>
                        </div>
                    </a>
                </div>
                <% } %>

                <% if (payroll?.foreignServiceIncome?.foreignServiceTaxExemption) { %>
                  <div class="item">
                      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/foreign-service-income" class="input-link">
                          <div class="item-header">
                              <i class="ph ph-globe"></i>
                              Foreign Service Income
                          </div>
                          <div class="item-value">
                              <span class="tag tax-exempt">Tax Exempt</span>
                              <% if (payroll.foreignServiceIncome.foreignServiceTaxExemption) { %>
                                  <span class="tag info">Section 10(1)(o)(ii)</span>
                              <% } %>
                          </div>
                      </a>
                  </div>
                  <% } %>

                  <% if (payroll?.employerLoan?.regularRepayment > 0) { %>
                    <div class="item">
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/employer-loan" class="input-link">
                            <div class="item-header">
                                <i class="ph ph-bank"></i>
                                Employer Loan
                            </div>
                            <div class="item-value">
                                R <%= Number(payroll.employerLoan.regularRepayment).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                                <% if (payroll.employerLoan.calculateInterestBenefit) { %>
                                    <span class="tag tax-benefit">Fringe Benefit</span>
                                <% } %>
                                <span class="tag info"><%= payroll.employerLoan.interestRate %>% Interest</span>
                            </div>
                        </a>
                    </div>
                    <% } %>

                    <% if (payroll?.taxDirective?.directiveNumber) { %>
                      <div class="item">
                          <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/tax-directive" class="input-link">
                              <div class="item-header">
                                  <i class="ph ph-file-text"></i>
                                  Tax Directive
                              </div>
                              <div class="item-value">
                                  <%= payroll.taxDirective.directiveNumber %>
                                  <% if (payroll.taxDirective.directiveType === 'Fixed Percentage - IRP3(b) and IRP3(pa)') { %>
                                      <span class="tag info"><%= payroll.taxDirective.percentage %>%</span>
                                  <% } else if (payroll.taxDirective.directiveType === 'Fixed Amount - IRP3(c)') { %>
                                      <span class="tag info">Fixed Amount</span>
                                  <% } %>
                                  <span class="tag tax-directive">SARS Directive</span>
                              </div>
                          </a>
                      </div>
                      <% } %>

                    <% if (payroll?.voluntaryTaxOverDeduction > 0) { %>
                      <div class="item">
                          <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/voluntary-tax-over-deduction" class="input-link">
                              <div class="item-header">
                                  <i class="ph ph-currency-circle-dollar"></i>
                                  Voluntary Tax Over Deduction
                              </div>
                              <div class="item-value">
                                  R <%= Number(payroll.voluntaryTaxOverDeduction).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                                  <span class="tag tax-deductible">Tax Deductible</span>
                              </div>
                          </a>
                      </div>
                      <% } %>

        <% if (payroll?.bursariesAndScholarships?.taxablePortion > 0) { %>
        <div class="item">
                <div class="item-header">
                    <i class="ph ph-graduation-cap"></i>
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/bursaries-and-scholarships">Bursaries and Scholarships</a>
                </div>
                <div class="item-value">
                    R <%= Number(payroll.bursariesAndScholarships.taxablePortion).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                    <span class="tag taxable">Taxable</span>
                </div>
        </div>
        <% } %>


        <!-- Deductions Section -->
<!-- Garnishee Order -->
<% if (payroll?.garnishee?.amount > 0) { %>
<div class="input-placeholder garnishee-order">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/garnishee" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-scales"></i>
              <span class="component-name">Garnishee Order</span>
          </div>
          <div class="placeholder-details">
              <span class="amount">R <%= parseFloat(payroll.garnishee.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Income Protection -->
<% if (currentPeriod?.data?.has('incomeProtection') && parseFloat(currentPeriod.data.get('incomeProtection').amount) > 0) { %>
<div class="input-placeholder income-protection">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/income-protection" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-shield-check"></i>
              <span class="component-name">Income Protection</span>
          </div>
          <div class="placeholder-details">
              <% const incomeProtection = currentPeriod.data.get('incomeProtection'); %>
              <span class="amount">R <%= parseFloat(incomeProtection.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Maintenance Order -->
<% if (currentPeriod?.data?.has('maintenanceOrder') && parseFloat(currentPeriod.data.get('maintenanceOrder').amount) > 0) { %>
<div class="input-placeholder maintenance-order">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/maintenance-order" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-gavel"></i>
              <span class="component-name">Maintenance Order</span>
          </div>
          <div class="placeholder-details">
              <% const maintenanceOrder = currentPeriod.data.get('maintenanceOrder'); %>
              <span class="amount">R <%= parseFloat(maintenanceOrder.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
              <span class="tag non-taxable">Non-Taxable</span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Medical Aid -->
<% if (currentPeriod?.data?.has('medicalAid') && parseFloat(currentPeriod.data.get('medicalAid').amount) > 0) { %>
<div class="input-placeholder medical-aid">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/medical-aid" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-first-aid"></i>
              <span class="component-name">Medical Aid</span>
          </div>
          <div class="placeholder-details">
              <% const medicalAid = currentPeriod.data.get('medicalAid'); %>
              <span class="amount">R <%= parseFloat(medicalAid.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Pension Fund -->
<% if (currentPeriod?.data?.has('pensionFund') && parseFloat(currentPeriod.data.get('pensionFund').amount) > 0) { %>
<div class="input-placeholder pension-fund">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/pension-fund" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-bank"></i>
              <span class="component-name">Pension Fund</span>
          </div>
          <div class="placeholder-details">
              <% const pensionFund = currentPeriod.data.get('pensionFund'); %>
              <span class="amount">R <%= parseFloat(pensionFund.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Provident Fund -->
<% if (currentPeriod?.data?.has('providentFund') && parseFloat(currentPeriod.data.get('providentFund').amount) > 0) { %>
<div class="input-placeholder provident-fund">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/provident-fund" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-bank"></i>
              <span class="component-name">Provident Fund</span>
          </div>
          <div class="placeholder-details">
              <% const providentFund = currentPeriod.data.get('providentFund'); %>
              <span class="amount">R <%= parseFloat(providentFund.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Retirement Annuity Fund -->
<% if (currentPeriod?.data?.has('retirementAnnuityFund') && parseFloat(currentPeriod.data.get('retirementAnnuityFund').amount) > 0) { %>
<div class="input-placeholder retirement-annuity-fund">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/retirement-annuity-fund" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-bank"></i>
              <span class="component-name">Retirement Annuity Fund</span>
          </div>
          <div class="placeholder-details">
              <% const retirementAnnuityFund = currentPeriod.data.get('retirementAnnuityFund'); %>
              <span class="amount">R <%= parseFloat(retirementAnnuityFund.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Union Membership Fee -->
<% if (currentPeriod?.data?.has('unionMembershipFee') && parseFloat(currentPeriod.data.get('unionMembershipFee').amount) > 0) { %>
<div class="input-placeholder union-membership-fee">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/union-membership-fee" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-users-three"></i>
              <span class="component-name">Union Membership Fee</span>
          </div>
          <div class="placeholder-details">
              <% const unionMembershipFee = currentPeriod.data.get('unionMembershipFee'); %>
              <span class="amount">R <%= parseFloat(unionMembershipFee.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Voluntary Tax Over Deduction -->
<% if (currentPeriod?.data?.has('voluntaryTaxOverDeduction') && parseFloat(currentPeriod.data.get('voluntaryTaxOverDeduction').amount) > 0) { %>
<div class="input-placeholder voluntary-tax-over-deduction">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/voluntary-tax-over-deduction" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-currency-circle-dollar"></i>
              <span class="component-name">Voluntary Tax Over Deduction</span>
          </div>
          <div class="placeholder-details" style="flex-direction: column; align-items: flex-end; gap: 4px;">
              <span class="amount">R <%= parseFloat(currentPeriod.data.get('voluntaryTaxOverDeduction').amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
              <div style="display: flex; gap: 4px; flex-wrap: wrap; justify-content: flex-end;">
                  <span class="tag tax-deductible">Tax Deductible</span>
              </div>
          </div>
      </div>
  </a>
</div>
<% } %>

<!-- Benefits Section -->
<!-- Accommodation Benefit -->
<% if (currentPeriod?.data?.has('accommodationBenefit') && parseFloat(currentPeriod.data.get('accommodationBenefit').amount) > 0) { %>
  <div class="input-placeholder accommodation-benefit">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/accommodation-benefit" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-house"></i>
                <span class="component-name">Accommodation Benefit</span>
            </div>
            <div class="placeholder-details">
                <% const accommodationBenefit = currentPeriod.data.get('accommodationBenefit'); %>
                <span class="amount">R <%= parseFloat(accommodationBenefit.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>
  
  <!-- Bursaries and Scholarships -->
  <% if (currentPeriod?.data?.has('bursariesAndScholarships') && parseFloat(currentPeriod.data.get('bursariesAndScholarships').amount) > 0) { %>
  <div class="input-placeholder bursaries-and-scholarships">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/bursaries-and-scholarships" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-student"></i>
                <span class="component-name">Bursaries and Scholarships</span>
            </div>
            <div class="placeholder-details">
                <% const bursariesAndScholarships = currentPeriod.data.get('bursariesAndScholarships'); %>
                <span class="amount">R <%= parseFloat(bursariesAndScholarships.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>
  
  <!-- Company Car -->
  <% if (currentPeriod?.data?.has('companyCar') && parseFloat(currentPeriod.data.get('companyCar').amount) > 0) { %>
  <div class="input-placeholder company-car">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/company-car" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-car"></i>
                <span class="component-name">Company Car</span>
            </div>
            <div class="placeholder-details">
                <% const companyCar = currentPeriod.data.get('companyCar'); %>
                <span class="amount">R <%= parseFloat(companyCar.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>
  
  <!-- Company Car Under Operating Lease -->
  <% if (currentPeriod?.data?.has('companyCarUnderOperatingLease') && parseFloat(currentPeriod.data.get('companyCarUnderOperatingLease').amount) > 0) { %>
  <div class="input-placeholder company-car-under-operating-lease">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/company-car-lease" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-car-profile"></i>
                <span class="component-name">Company Car (Operating Lease)</span>
            </div>
            <div class="placeholder-details">
                <% const companyCarLease = currentPeriod.data.get('companyCarUnderOperatingLease'); %>
                <span class="amount">R <%= parseFloat(companyCarLease.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>
  
  <!-- Other Section -->
  <!-- Employer Loan -->
  <% if (payroll?.employerLoan?.regularRepayment > 0) { %>
  <div class="input-placeholder employer-loan">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/employer-loan" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-bank"></i>
                <span class="component-name">Employer Loan</span>
            </div>
            <div class="placeholder-details" style="flex-direction: column; align-items: flex-end; gap: 4px;">
                <span class="amount">R <%= parseFloat(payroll.employerLoan.regularRepayment).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
                <div style="display: flex; gap: 4px; flex-wrap: wrap; justify-content: flex-end;">
                    <% if (payroll.employerLoan.calculateInterestBenefit) { %>
                        <span class="tag tax-benefit">Fringe Benefit</span>
                    <% } %>
                    <span class="tag info"><%= payroll.employerLoan.interestRate %>% Interest</span>
                </div>
            </div>
        </div>
    </a>
  </div>
  <% } %>
  
  <!-- Foreign Service Income -->
  <% if (currentPeriod?.data?.has('foreignServiceIncome') && parseFloat(currentPeriod.data.get('foreignServiceIncome').amount) > 0) { %>
  <div class="input-placeholder foreign-service-income">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/foreign-service-income" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-globe"></i>
                <span class="component-name">Foreign Service Income</span>
            </div>
            <div class="placeholder-details">
                <% const foreignServiceIncome = currentPeriod.data.get('foreignServiceIncome'); %>
                <span class="amount">R <%= parseFloat(foreignServiceIncome.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>
  
  <!-- Savings -->
  <% if (currentPeriod?.data?.has('savings') && parseFloat(currentPeriod.data.get('savings').amount) > 0) { %>
  <div class="input-placeholder savings">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/savings" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-piggy-bank"></i>
                <span class="component-name">Savings</span>
            </div>
            <div class="placeholder-details">
                <% const savings = currentPeriod.data.get('savings'); %>
                <span class="amount">R <%= parseFloat(savings.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>
  
  <!-- Tax Directive -->
  <% if (currentPeriod?.data?.has('taxDirective') && parseFloat(currentPeriod.data.get('taxDirective').amount) > 0) { %>
  <div class="input-placeholder tax-directive">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/tax-directive" class="placeholder-link">
        <div class="placeholder-content">
            <div class="placeholder-header">
                <i class="ph ph-file-text"></i>
                <span class="component-name">Tax Directive</span>
            </div>
            <div class="placeholder-details">
                <% const taxDirective = currentPeriod.data.get('taxDirective'); %>
                <span class="amount">R <%= parseFloat(taxDirective.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
            </div>
        </div>
    </a>
  </div>
  <% } %>
  <!-- Income Section -->
     <!-- Commission -->
<% if (currentPeriod?.data?.has('commission') && parseFloat(currentPeriod.data.get('commission').amount) > 0) { %>
<div class="input-placeholder commission">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/commission" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-trending-up"></i>
              <span class="component-name">Commission</span>
          </div>
          <div class="placeholder-details">
              <% const commission = currentPeriod.data.get('commission'); %>
              <span class="amount">R <%= parseFloat(commission.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<% if (currentPeriod?.data?.has('lossOfIncome') && parseFloat(currentPeriod.data.get('lossOfIncome').amount) > 0) { %>
<div class="input-placeholder loss-of-income">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/loss-of-income" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-trending-down"></i>
              <span class="component-name">Loss of Income</span>
          </div>
          <div class="placeholder-details">
              <% const lossOfIncome = currentPeriod.data.get('lossOfIncome'); %>
              <span class="amount">R <%= parseFloat(lossOfIncome.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<% if (currentPeriod?.voluntaryTaxOverDeduction > 0) { %>
<div class="input-placeholder voluntary-tax">
  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/voluntary-tax-over-deduction" class="placeholder-link">
      <div class="placeholder-content">
          <div class="placeholder-header">
              <i class="ph ph-money"></i>
              <span class="component-name">Voluntary Tax Over Deduction</span>
          </div>
          <div class="placeholder-details">
              <span class="amount">R <%= parseFloat(currentPeriod.voluntaryTaxOverDeduction).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %></span>
          </div>
      </div>
  </a>
</div>
<% } %>

<% if (payroll?.garnishee > 0) { %>
  <div class="item">
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/garnishee" class="input-link">
      <div class="item-header">
        <i class="ph ph-scales"></i>
        Garnishee
        <% if (payroll?.data?.get('garnisheeDate')) { %>
          <span class="date-tag">
            <%= new Date(payroll.data.get('garnisheeDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
          </span>
        <% } %>
      </div>
      <div class="item-value">
        R <%= Number(payroll.garnishee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
        <span class="tag non-taxable">Non-Taxable</span>
      </div>
    </a>
  </div>
<% } %>








        <!-- Hours Input (if hourly paid) -->



        <!-- Custom Income Items (configured quantity-based) -->
        <% if (payroll?.customIncomeItems && payroll.customIncomeItems.length > 0) { %>
            <%
            // Filter for configured custom income items
            const configuredCustomIncomeItems = payroll.customIncomeItems.filter(function(item) {
                return item.inputType === 'customRateQuantity' &&
                       item.customIncomeId &&
                       item.name;
            });
            %>
            <% configuredCustomIncomeItems.forEach(function(customIncomeItem) { %>
                <div class="item">
                    <div class="item-header">
                        <i class="ph ph-plus-circle"></i>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/custom-income/<%= customIncomeItem.customIncomeId %>?mode=edit"><%= customIncomeItem.name %> (Custom Income)</a>
                    </div>
                    <div class="item-value">
                        <% if (customIncomeItem.calculatedAmount && customIncomeItem.calculatedAmount > 0) { %>
                            R <%= Number(customIncomeItem.calculatedAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                            <span class="tag taxable">Taxable</span>
                            <% if (customIncomeItem.quantity) { %>
                                <span class="tag info">Qty: <%= customIncomeItem.quantity %></span>
                            <% } %>
                        <% } else { %>
                            <span class="item-value empty">Configured - Set quantity on payslip</span>
                        <% } %>
                    </div>
                </div>
            <% }); %>
        <% } %>

    </div>
</div>


  <!-- Payslip Inputs Card -->
  <div class="input-card <%= currentPeriod?.isFinalized ? 'finalized-period' : '' %>">
    <div class="header">
        <h2>Payslip Inputs</h2>
        <% if (!currentPeriod?.isFinalized) { %>
            <button class="add-button">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff">
                    <i class="ph ph-plus"></i> Add
                </a>
            </button>
        <% } %>
    </div>

    

    <div class="payslip-items">
        <% if (payroll?.travelAllowance?.reimbursedExpenses) { %>
            <div class="input-placeholder travel-expenses">
                <% if (currentPeriod?.isFinalized) { %>
                    <div class="placeholder-link finalized-input">
                        <div class="placeholder-content">
                            <div class="placeholder-header">
                                <i class="ph ph-car"></i>
                                <span class="component-name">Travel Expenses</span>
                            </div>
                        </div>
                    </div>
                <% } else { %>
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/travel-expenses" class="placeholder-link">
                        <div class="placeholder-content">
                            <div class="placeholder-header">
                                <i class="ph ph-car"></i>
                                <span class="component-name">Travel Expenses</span>
                            </div>
                        </div>
                    </a>
                <% } %>
            </div>
        <% } %>
        <!-- Once-off Items -->
  <% if (payroll?.hourlyPaid) { %>
    <div class="input-placeholder hourly-salary">
        <% if (currentPeriod?.isFinalized) { %>
            <div class="placeholder-link finalized-input">
                <div class="placeholder-content">
                    <div class="placeholder-header">
                        <i class="ph ph-clock-countdown"></i>
                        <span class="component-name">Hourly Salary Input</span>
                    </div>
                    <% if (hourlyRates) { %>
                        <div class="placeholder-details">
                            <p>Normal Hours: <%= hourlyRates.normalHours || 0 %></p>
                            <p>Overtime Hours: <%= hourlyRates.overtimeHours || 0 %></p>
                            <% if (hourlyRates.weeks && hourlyRates.weeks.length > 0) { %>
                                <p>Sunday Hours: <%= hourlyRates.weeks.reduce((total, week) => total + (Number(week.sundayHours) || 0), 0) %></p>
                            <% } %>
                        </div>
                    <% } %>
                </div>
            </div>
        <% } else { %>
            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/basicSalaryHourly" class="placeholder-link">
                <div class="placeholder-content">
                    <div class="placeholder-header">
                        <i class="ph ph-clock-countdown"></i>
                        <span class="component-name">Hourly Salary Input</span>
                    </div>
                    <% if (hourlyRates) { %>
                        <div class="placeholder-details">
                            <p>Normal Hours: <%= hourlyRates.normalHours || 0 %></p>
                            <p>Overtime Hours: <%= hourlyRates.overtimeHours || 0 %></p>
                            <% if (hourlyRates.weeks && hourlyRates.weeks.length > 0) { %>
                                <p>Sunday Hours: <%= hourlyRates.weeks.reduce((total, week) => total + (Number(week.sundayHours) || 0), 0) %></p>
                            <% } %>
                        </div>
                    <% } %>
                </div>
            </a>
        <% } %>
    </div>
<% } %>

        <!-- Custom Income Quantity Input Placeholders -->
        <% if (payroll?.customIncomeItems && payroll.customIncomeItems.length > 0) { %>
            <%
            // Filter for configured custom income items
            const configuredCustomIncomeItems = payroll.customIncomeItems.filter(function(item) {
                return item.inputType === 'customRateQuantity' &&
                       item.customIncomeId &&
                       item.name;
            });
            %>
            <% configuredCustomIncomeItems.forEach(function(customIncomeItem) { %>
                <div class="input-placeholder custom-income-quantity">
                    <% if (currentPeriod?.isFinalized) { %>
                        <div class="placeholder-link finalized-input">
                            <div class="placeholder-content">
                                <div class="placeholder-header">
                                    <i class="ph ph-plus-circle"></i>
                                    <span class="component-name"><%= customIncomeItem.name %> - Quantity</span>
                                </div>
                                <div class="placeholder-details">
                                    <% if (customIncomeItem.quantity) { %>
                                        <p>Current Quantity: <%= customIncomeItem.quantity %></p>
                                        <% if (customIncomeItem.customRate) { %>
                                            <p>Rate: R <%= Number(customIncomeItem.customRate).toFixed(2) %></p>
                                            <p>Amount: R <%= Number(customIncomeItem.calculatedAmount || 0).toFixed(2) %></p>
                                        <% } %>
                                    <% } else { %>
                                        <p>No quantity set for this period</p>
                                        <% if (customIncomeItem.customRate) { %>
                                            <p>Rate: R <%= Number(customIncomeItem.customRate).toFixed(2) %></p>
                                        <% } %>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    <% } else { %>
                        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/custom-income-quantity/<%= customIncomeItem.customIncomeId %>" class="placeholder-link">
                            <div class="placeholder-content">
                                <div class="placeholder-header">
                                    <i class="ph ph-plus-circle"></i>
                                    <span class="component-name"><%= customIncomeItem.name %> - Quantity</span>
                                </div>
                                <div class="placeholder-details">
                                    <% if (customIncomeItem.quantity) { %>
                                        <p>Current Quantity: <%= customIncomeItem.quantity %></p>
                                        <% if (customIncomeItem.customRate) { %>
                                            <p>Rate: R <%= Number(customIncomeItem.customRate).toFixed(2) %></p>
                                            <p>Amount: R <%= Number(customIncomeItem.calculatedAmount || 0).toFixed(2) %></p>
                                        <% } %>
                                    <% } else { %>
                                        <p>Click to set quantity for this pay period</p>
                                        <% if (customIncomeItem.customRate) { %>
                                            <p>Rate: R <%= Number(customIncomeItem.customRate).toFixed(2) %></p>
                                        <% } %>
                                    <% } %>
                                </div>
                            </div>
                        </a>
                    <% } %>
                </div>
            <% }); %>
        <% } %>

<style>
.placeholder-details {
    padding: 10px;
    font-size: 0.9em;
    color: #666;
}

.placeholder-details p {
    margin: 5px 0;
}

/* Period Modal Styles - Enhanced for Finalized/Unfinalized Periods */
.period-item.unfinalized {
    border-left: 4px solid #10b981; /* Green border for unfinalized periods */
    background-color: #f0fdf4; /* Light green background */
}

.period-item.finalized {
    border-left: 4px solid #6b7280; /* Gray border for finalized periods */
    background-color: #f9fafb; /* Light gray background */
}

.period-status.pending {
    background-color: #10b981;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.period-status.finalized {
    background-color: #6b7280;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status.pending {
    color: #10b981;
    font-weight: 500;
}

.status.completed {
    color: #6b7280;
    font-weight: 500;
}

.period-item.unfinalized:hover {
    background-color: #dcfce7; /* Darker green on hover */
    transform: translateX(5px);
    transition: all 0.2s ease;
}

.period-item.finalized:hover {
    background-color: #f3f4f6; /* Darker gray on hover */
    transform: translateX(5px);
    transition: all 0.2s ease;
}

/* SIMPLE FIX: Removed business rule engine generated periods styles */

.period-item.generated:hover {
    background-color: #dbeafe; /* Darker blue on hover */
    transform: translateX(5px);
    transition: all 0.2s ease;
}

.period-item.generated .period-status {
    background-color: #3b82f6;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
}
</style>
        <!-- Once-off Items Section -->
        <% 
        const hasOnceOffItems = 
            (payroll?.annualBonus?.amount > 0) ||
            (payroll?.arbitrationAward?.amount > 0) ||
            (payroll?.dividendsSubject?.amount > 0) ||
            (payroll?.extraPay?.amount > 0) ||
            (payroll?.leavePaidOut?.amount > 0) ||
            (payroll?.restraintOfTrade?.amount > 0);
        
        const getOnceOffItems = (payroll) => {
            const items = [];
            
            if (payroll?.annualBonus?.amount > 0) {
                items.push({
                    type: 'earning',
                    description: payroll.annualBonus.description || 'Annual Bonus',
                    amount: payroll.annualBonus.amount,
                    date: payroll.annualBonus.date
                });
            }
            
            if (payroll?.arbitrationAward?.amount > 0) {
                items.push({
                    type: 'earning',
                    description: payroll.arbitrationAward.description || 'Arbitration Award',
                    amount: payroll.arbitrationAward.amount,
                    date: payroll.arbitrationAward.date
                });
            }
        
            // Add other once-off items similarly
            return items;
        }
        %>
        
        <% if (hasOnceOffItems) { %>
            <% getOnceOffItems(payroll).forEach(item => { %>
                <div class="item">
                    <div class="item-header">
                        <i class="ph <%= item.type === 'deduction' ? 'ph-minus-circle' : 'ph-plus-circle' %>"></i>
                        <%= item.description %>
                    </div>
                    <div class="item-value <%= item.type === 'deduction' ? 'deduction' : '' %>">
                        <%= item.type === 'deduction' ? '-' : '+' %> R <%= Number(item.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                    </div>
                </div>
            <% }); %>
        <% } else { %>
            <div class="empty-state">
                <i class="ph ph-receipt"></i>
                <p>No once-off items for this period</p>
                <% if (!currentPeriod?.isFinalized) { %>
                    <!-- Add any additional content for non-finalized periods -->
                <% } %>
            </div>
        <% } %>
        
        <!-- Annual Bonus Display -->
        <% if (payroll?.annualBonus?.amount > 0) { %>
            <div class="item">
                <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/annualBonus" class="input-link">
                    <div class="item-header">
                        <i class="ph ph-confetti"></i>
                        Annual Bonus
                    </div>
                    <div class="item-value">
                        R <%= Number(payroll.annualBonus.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
                        <span class="tag taxable">Taxable</span>
                        <% if (payroll.annualBonus.date) { %>
                            <span class="tag info"><%= new Date(payroll.annualBonus.date).toLocaleDateString('en-ZA') %></span>
                        <% } %>
                    </div>
                </a>
            </div>
        <% } %>
        <!-- Annual Payment -->
<% if (payroll?.annualPayment?.amount > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/annual-payment" class="input-link">
          <div class="item-header">
              <i class="ph ph-currency-circle-dollar"></i>
              Annual Payment
          </div>
          <div class="item-value">
              R <%= Number(payroll.annualPayment.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
              <% if (payroll.annualPayment.date) { %>
                  <span class="tag info"><%= new Date(payroll.annualPayment.date).toLocaleDateString('en-ZA') %></span>
              <% } %>
          </div>
      </a>
  </div>
<% } %>

<!-- Dividends Subject -->
<% if (payroll?.dividendsSubject?.amount > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/dividends-subject" class="input-link">
          <div class="item-header">
              <i class="ph ph-chart-pie"></i>
              Dividends Subject
          </div>
          <div class="item-value">
              R <%= Number(payroll.dividendsSubject.amount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
              <% if (payroll.dividendsSubject.directiveIssueDate) { %>
                  <span class="tag info"><%= new Date(payroll.dividendsSubject.directiveIssueDate).toLocaleDateString('en-ZA') %></span>
              <% } %>
          </div>
      </a>
  </div>
<% } %>

<!-- Extra Pay -->
<% if (payroll?.extraPay > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/extra-pay" class="input-link">
          <div class="item-header">
              <i class="ph ph-plus-circle"></i>
              Extra Pay
          </div>
          <div class="item-value">
              R <%= Number(payroll.extraPay).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Restraint of Trade -->
<% if (payroll?.restraintOfTrade > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/restraint-of-trade" class="input-link">
          <div class="item-header">
              <i class="ph ph-handshake"></i>
              Restraint of Trade
          </div>
          <div class="item-value">
              R <%= Number(payroll.restraintOfTrade).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>
<!-- Broad Based Share Plan -->
<% if (payroll?.broadBasedEmployeeSharePlan > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/broad-based-share-plan" class="input-link">
          <div class="item-header">
              <i class="ph ph-chart-line-up"></i>
              Broad Based Share Plan
          </div>
          <div class="item-value">
              R <%= Number(payroll.broadBasedEmployeeSharePlan).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Computer Allowance -->
<% if (payroll?.computerAllowance > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/computer-allowance" class="input-link">
          <div class="item-header">
              <i class="ph ph-desktop"></i>
              Computer Allowance
          </div>
          <div class="item-value">
              R <%= Number(payroll.computerAllowance).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Expense Claim -->
<% if (payroll?.expenseClaim > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/expense-claim" class="input-link">
          <div class="item-header">
              <i class="ph ph-receipt"></i>
              Expense Claim
          </div>
          <div class="item-value">
              R <%= Number(payroll.expenseClaim).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag non-taxable">Non-Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Gain Vesting -->
<% if (payroll?.gainVesting?.directiveIncomeAmount > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/gain-vesting" class="input-link">
          <div class="item-header">
              <i class="ph ph-trend-up"></i>
              Gain Vesting
          </div>
          <div class="item-value">
              R <%= Number(payroll.gainVesting.directiveIncomeAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
              <% if (payroll.gainVesting.date) { %>
                  <span class="tag info"><%= new Date(payroll.gainVesting.date).toLocaleDateString('en-ZA') %></span>
              <% } %>
          </div>
      </a>
  </div>
<% } %>

<!-- Phone Allowance -->
<% if (payroll?.phoneAllowance > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/phone-allowance" class="input-link">
          <div class="item-header">
              <i class="ph ph-phone"></i>
              Phone Allowance
          </div>
          <div class="item-value">
              R <%= Number(payroll.phoneAllowance).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Relocation Allowance -->
<% if (payroll?.relocationAllowance?.taxableAmount > 0 || payroll?.relocationAllowance?.nonTaxableAmount > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/relocation-allowance" class="input-link">
          <div class="item-header">
              <i class="ph ph-house-simple"></i>
              Relocation Allowance
          </div>
          <div class="item-value">
              R <%= Number(payroll.relocationAllowance.taxableAmount + payroll.relocationAllowance.nonTaxableAmount).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <% if (payroll.relocationAllowance.taxableAmount > 0) { %>
                  <span class="tag taxable">Partially Taxable</span>
              <% } else { %>
                  <span class="tag non-taxable">Non-Taxable</span>
              <% } %>
          </div>
      </a>
  </div>
<% } %>

<!-- Subsistence International -->
<% if (payroll?.subsistenceAllowanceInternational?.totalPaidToEmployee > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/subsistence-international" class="input-link">
          <div class="item-header">
              <i class="ph ph-airplane"></i>
              International Subsistence
          </div>
          <div class="item-value">
              R <%= Number(payroll.subsistenceAllowanceInternational.totalPaidToEmployee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag non-taxable">Non-Taxable</span>
              <span class="tag info"><%= payroll.subsistenceAllowanceInternational.numberOfDays %> days</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Subsistence Local -->
<% if (payroll?.subsistenceAllowanceLocal?.fullAmountPaidToEmployee > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/subsistence-local" class="input-link">
          <div class="item-header">
              <i class="ph ph-map-pin"></i>
              Local Subsistence
          </div>
          <div class="item-value">
              R <%= Number(payroll.subsistenceAllowanceLocal.fullAmountPaidToEmployee).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag non-taxable">Non-Taxable</span>
              <span class="tag info"><%= payroll.subsistenceAllowanceLocal.numberOfDays %> days</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Tool Allowance -->
<% if (payroll?.toolAllowance > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/tool-allowance" class="input-link">
          <div class="item-header">
              <i class="ph ph-wrench"></i>
              Tool Allowance
          </div>
          <div class="item-value">
              R <%= Number(payroll.toolAllowance).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>

<!-- Uniform Allowance -->
<% if (payroll?.uniformAllowance > 0) { %>
  <div class="item">
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/onceOff/uniform-allowance" class="input-link">
          <div class="item-header">
              <i class="ph ph-t-shirt"></i>
              Uniform Allowance
          </div>
          <div class="item-value">
              R <%= Number(payroll.uniformAllowance).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
              <span class="tag taxable">Taxable</span>
          </div>
      </a>
  </div>
<% } %>
<% if (payroll?.commissionEnabled) { %>
  <div class="item">
    <% if (payroll?.commission > 0) { %>
      <div class="item-header">
        <i class="ph ph-trending-up"></i>
        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/commission">Commission</a>
        <% if (payroll?.data?.get('commissionDate')) { %>
          <span class="date-tag">
            <%= new Date(payroll.data.get('commissionDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
          </span>
        <% } %>
      </div>
      <div class="item-value">
        R <%= Number(payroll.commission).toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) %>
        <span class="tag taxable">Taxable</span>
      </div>
    <% } else { %>
      <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/commission" class="input-link">
        <div class="item-header">
          <i class="ph ph-trending-up"></i>
          Commission
          <% if (payroll?.data?.get('commissionDate')) { %>
            <span class="date-tag">
              <%= new Date(payroll.data.get('commissionDate')).toLocaleDateString('en-ZA', { day: 'numeric', month: 'short', year: 'numeric' }) %>
            </span>
          <% } %>
        </div>
        <div class="item-value empty">Set on payslip</div>
      </a>
    <% } %>
  </div>
<% } %>
    </div>
    </div>


  <!-- Calculator Card -->
  <div class="section" id="calculator">
      <div class="header">
          <h2>Calculator</h2>
          <button id="selectMonthButton" class="selectButton" data-employee-id="<%= employee._id %>">
            <div class="period-display">
                <% if (currentPeriod) { %>
                    <%
                    // Use BusinessDate field for timezone-independent display
                    const currentPeriodEndDateBusiness = currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD');
                    %>
                    <%= moment(currentPeriodEndDateBusiness).format('DD/MM/YYYY') %>
                    <span class="status-indicator <%= currentPeriod.isFinalized ? 'completed' : 'pending' %>">
                        <%= currentPeriod.isFinalized ? 'Finalized' : 'Pending' %>
                    </span>
                <% } %>
            </div>
            <i class="ph ph-caret-down"></i>
        </button>
      </div>
      <div class="pagination-controls">
        <button class="nav-button prev" onclick="prevPage()">
          <i class="ph ph-caret-left"></i>
        </button>
        <span class="page-indicator">1 of 4</span>
        <button class="nav-button next" onclick="nextPage()">
          <i class="ph ph-caret-right"></i>
        </button>
      </div>

      <div class="calculator-container">
        <% if (payrollDetails) { %>
        <!-- Page 1: Income Section -->
        <div class="calc-page" data-page="1">
          <div class="calc-group basic-salary">
            <h3><i class="ph ph-money"></i> Basic Salary</h3>
            <div class="row">
              <%
              // Determine label based on whether pro-rata is applied
              let salaryLabel = "Basic Salary";
              if (typeof proratedSalaryResult !== 'undefined' && proratedSalaryResult && proratedSalaryResult.isFirstPeriodWithDOA && Number(proratedSalaryResult.proratedPercentage) < 100) {
                salaryLabel = "Prorated Amount";
              }
              %>
              <label><%= salaryLabel %> (<%= employee.payFrequency.frequency %>):</label>
              <%
              // Pro-rata calculation with error handling
              let displaySalary = basicSalary || 0;
              let showProRataDetails = false;
              let proRataInfo = null;

              try {
                // Check if we have BusinessDate pro-rata results
                if (typeof proratedSalaryResult !== 'undefined' && proratedSalaryResult && proratedSalaryResult.isFirstPeriodWithDOA) {
                  const percentage = Number(proratedSalaryResult.proratedPercentage);

                  // Only show pro-rata details if percentage is not 100%
                  if (percentage < 100) {
                    displaySalary = Number(proratedSalaryResult.proratedSalary);
                    showProRataDetails = true;
                    proRataInfo = {
                      workedDays: proratedSalaryResult.workedDays,
                      totalDays: proratedSalaryResult.totalDaysInPeriod,
                      percentage: percentage,
                      fullSalary: Number(proratedSalaryResult.fullPeriodSalary),
                      proratedAmount: Number(proratedSalaryResult.proratedSalary)
                    };
                  } else {
                    // If 100%, just show basic salary without pro-rata details
                    displaySalary = basicSalary || Number(proratedSalaryResult.fullPeriodSalary);
                    showProRataDetails = false;
                  }
                }
              } catch (error) {
                console.error("Pro-rata calculation error:", error);
                // Fallback to basic salary
                displaySalary = basicSalary || 0;
                showProRataDetails = false;
              }
              %>
              <span>R <%= Number(displaySalary).toFixed(2) %></span>
            </div>

            <% if (showProRataDetails && proRataInfo) { %>
              <!-- Pro-rated Details -->
              <div class="row">
                <label>Pro-rata Details:</label>
                <span><%= proRataInfo.workedDays %> of <%= proRataInfo.totalDays %> days (<%= proRataInfo.percentage.toFixed(2) %>%)</span>
              </div>
              <div class="row prorated-amount">
                <label>Full Period Salary:</label>
                <span>R <%= proRataInfo.fullSalary.toFixed(2) %></span>
              </div>
              <div class="row prorated-amount">
                <label>Prorated Amount:</label>
                <span>R <%= proRataInfo.proratedAmount.toFixed(2) %></span>
              </div>
            <% } %>

            <!-- Accommodation Benefit Section -->
            <% if (payroll && payroll.accommodationBenefit && payroll.accommodationBenefit > 0) { %>
            <div class="row">
              <label>Accommodation Benefit:</label>
              <span>R <%= Number(payroll.accommodationBenefit).toFixed(2) %></span>
            </div>
            <div class="row sub-item">
              <label>Taxable Portion (100%):</label>
              <div class="allowance-info">
                <span>R <%= Number(payroll.accommodationBenefit).toFixed(2) %></span>
                <span class="tag taxable">Taxable</span>
              </div>
            </div>
            <% } %>

            <!-- Loss of Income Section -->
            <% if (payroll && payroll.lossOfIncome && payroll.lossOfIncome > 0) { %>
            <div class="row">
              <label>Loss of Income:</label>
              <span>R <%= Number(payroll.lossOfIncome).toFixed(2) %></span>
            </div>
            <% } %>

            <% if (travelAllowance && travelAllowance.calculationDetails) { %>
            <% const totalAllowance = Number(travelAllowance.calculationDetails.totalAllowance || 0);
               const taxableAmount = Number(travelAllowance.calculationDetails.taxableAmount || 0);
            %>
            <% if (totalAllowance > 0) { %>
              <div class="row">
                <label>Travel Allowance:</label>
                <span>R <%= totalAllowance.toFixed(2) %></span>
              </div>
            <% } %>
            <% if (taxableAmount > 0) { %>
              <div class="row">
                <label>Taxable Amount:</label>
                <span>R <%= taxableAmount.toFixed(2) %></span>
              </div>
            <% } %>
            <% } %>

            <!-- Total Income -->
            <div class="row total">
              <label>Total Income:</label>
              <span>R <%= Number(payrollDetails?.totalIncome || (isProrated ? payrollDetails.proratedAmount : totalIncome) + (payroll.travelAllowance?.fixedAmount || 0) || 0).toFixed(2) %></span>
            </div>
          </div>
        </div>

        <!-- Page 2: Allowances Section -->
        <div class="calc-page" data-page="2">
          <div class="calc-group allowances">
            <h3><i class="ph ph-car"></i> Allowances</h3>
            <% 
              const travelAllowanceAmount = Number(payroll?.travelAllowance?.fixedAllowanceAmount || 0);
              if (travelAllowanceAmount > 0) { 
            %>
              <div class="row" <%- travelAllowanceAmount === 0 ? 'style="display: none;"' : '' %>>
                <label>Travel Allowance:</label>
                <div class="allowance-info">
                  <span>R <%= travelAllowanceAmount.toFixed(2) %></span>
                </div>
              </div>
              <!-- Taxable Portion -->
              <div class="row sub-item">
                <label>Taxable Portion (<%= payroll?.travelAllowance?.only20PercentTax ? '20%' : '80%' %>):</label>
                <div class="allowance-info">
                  <span>R <%= (travelAllowanceAmount * (payroll?.travelAllowance?.only20PercentTax ? 0.2 : 0.8)).toFixed(2) %></span>
                  <span class="tag taxable">Taxable</span>
                </div>
              </div>
              <!-- Non-taxable Portion -->
              <div class="row sub-item">
                <label>Non-taxable Portion (<%= payroll?.travelAllowance?.only20PercentTax ? '80%' : '20%' %>):</label>
                <div class="allowance-info">
                  <span>R <%= (travelAllowanceAmount * (payroll?.travelAllowance?.only20PercentTax ? 0.8 : 0.2)).toFixed(2) %></span>
                  <span class="tag non-taxable">Non-taxable</span>
                </div>
              </div>
            <% } else { %>
              <div class="empty-state">
                <p>No allowances for this period</p>
              </div>
            <% } %>
          </div>
          
          <!-- Benefits Section -->
          <div class="calc-group benefits">
            <h3><i class="ph ph-house"></i> Benefits</h3>
            <% 
              const accommodationBenefitAmount = Number(payroll?.accommodationBenefit || 0);
              const hasAnyBenefits = accommodationBenefitAmount > 0;
              
              if (hasAnyBenefits) { 
            %>
              <% if (accommodationBenefitAmount > 0) { %>
                <div class="row">
                  <label>Accommodation Benefit:</label>
                  <div class="allowance-info">
                    <span>R <%= accommodationBenefitAmount.toFixed(2) %></span>
                  </div>
                </div>
                <!-- Taxable Portion -->
                <div class="row sub-item">
                  <label>Taxable Portion (100%):</label>
                  <div class="allowance-info">
                    <span>R <%= accommodationBenefitAmount.toFixed(2) %></span>
                    <span class="tag taxable">Taxable</span>
                  </div>
                </div>
              <% } %>
            <% } else { %>
              <div class="empty-state">
                <p>No benefits for this period</p>
              </div>
            <% } %>
          </div>
        </div>

        <!-- Page 3: Deductions Section -->
        <div class="calc-page" data-page="3">
          <div class="calc-group deductions">
            <h3><i class="ph ph-minus-circle"></i> Deductions</h3>
            <!-- PAYE -->
            <div class="row">
              <label>PAYE Tax:</label>
              <span>R <%= Number(totalPAYE || 0).toFixed(2) %></span>
            </div>
            <!-- UIF -->
            <div class="row">
              <label>UIF:</label>
              <span>R <%= Number(totalUIF || 0).toFixed(2) %></span>
            </div>
            <!-- Total Deductions -->
            <div class="row total">
              <label>Total Deductions:</label>
              <span>R <%= Number(totalDeductions || 0).toFixed(2) %></span>
            </div>
          </div>
        </div>

        <!-- Page 4: Summary Section -->
        <div class="calc-page" data-page="4">
          <div class="calc-group summary">
            <h3><i class="ph ph-calculator"></i> Summary</h3>
            <div class="row">
              <label>Total Income (<%= employee.payFrequency.frequency %>):</label>
              <span>R <%= Number(payrollDetails?.totalIncome || (isProrated ? payrollDetails.proratedAmount : totalIncome) || 0).toFixed(2) %></span>
            </div>
            <div class="row">
              <label>Total Deductions:</label>
              <span>R <%= Number(payrollDetails?.totalDeductions || 0).toFixed(2) %></span>
            </div>
            <div class="row total">
              <label>Nett Pay:</label>
              <span>R <%= Number((payrollDetails?.totalIncome || (isProrated ? payrollDetails.proratedAmount : totalIncome)) - payrollDetails.totalDeductions || 0).toFixed(2) %></span>
            </div>
          </div>
        </div>
        <% } else { %>
          <p>No payroll details available for this period.</p>
        <% } %>
      </div>
  </div>

  <!-- Enhanced Travel Allowance Calculator (BETA) - Temporarily disabled due to missing partial
  <%# include('partials/enhancedTravelAllowanceCalculator', {
    payroll: payroll,
    employee: employee,
    currentPeriod: currentPeriod
  }) %> -->

  <!-- Payroll Actions Card -->
  <div class="payroll-actions-card">
    <div class="payroll-actions-header">
      <i class="ph ph-gear"></i>
      <h2>Payroll Actions</h2>
    </div>

    <div class="payroll-actions-content">
      <!-- Dynamic form based on period finalization status -->
      <% if (currentPeriod && currentPeriod.isFinalized) { %>
        <!-- Unfinalize form -->
        <form id="finaliseForm" method="post" action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/unfinalize">
            <input type="hidden" name="employeeId" value="<%= employee._id %>" />
            <input type="hidden" name="currentPeriodEndDate" value="<%= currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD') %>" />
            <input type="hidden" name="payFrequency" value="<%= JSON.stringify(employee.payFrequency) %>">
            <input type="hidden" name="_csrf" value="<%= csrfToken %>" />

            <button id="finaliseButton" type="submit" class="unfinalize-button">
                <i class="ph ph-arrow-counter-clockwise"></i>
                Unfinalize Period
            </button>
        </form>
      <% } else { %>
        <!-- Finalize form (existing logic) -->
        <form id="finaliseForm" method="post" action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/finalize">
            <input type="hidden" name="employeeId" value="<%= employee._id %>" />

            <% if (currentPeriod && currentPeriod.endDate) { %>
                <input type="hidden" name="currentPeriodEndDate" value="<%= currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD') %>" />
            <% } else { %>
                <!-- Provide a default or handle the case when no period exists -->
                <input type="hidden" name="currentPeriodEndDate" value="<%= moment.utc().endOf('month').format('YYYY-MM-DD') %>" />
            <% } %>

            <input type="hidden" name="payFrequency" value="<%= JSON.stringify(employee.payFrequency) %>">
            <input type="hidden" name="_csrf" value="<%= csrfToken %>" />

            <!-- Disable the button if no period exists -->
            <button id="finaliseButton" type="submit" <%= !currentPeriod ? 'disabled' : '' %>>
                <i class="ph ph-check-circle"></i>
                <%= !currentPeriod ? 'No Period to Finalise' : 'Finalise Period' %>
            </button>
        </form>
      <% } %>

      <div class="downloadPayslip">
        <button onclick="confirmDownloadPayslip('<%= employee._id %>', '<%= currentPeriod ? (currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format('YYYY-MM-DD')) : moment.utc(thisMonth).format('YYYY-MM-DD') %>', '<%= currentPeriod ? currentPeriod.isFinalized : false %>')">
          <i class="ph ph-download-simple"></i>
          Download Payslip
        </button>
      </div>
    </div>
  </div>
</section>

  <!-- Toast Notification Container -->
  <div id="toast-container" class="toast-container"></div>

  <!-- Confirmation Dialog -->
  <div id="confirmation-overlay" class="confirmation-overlay">
    <div class="confirmation-dialog">
      <div class="confirmation-header">
        <h3 id="confirmation-title" class="confirmation-title"></h3>
      </div>
      <div class="confirmation-body">
        <div id="confirmation-message" class="confirmation-message"></div>
        <div id="confirmation-details" class="confirmation-details" style="display: none;"></div>
      </div>
      <div class="confirmation-actions">
        <button id="confirmation-cancel" class="confirmation-btn cancel">Cancel</button>
        <button id="confirmation-confirm" class="confirmation-btn confirm">Confirm</button>
      </div>
    </div>
  </div>

  <!-- Month Selection Modal -->
  <div id="monthModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Select a Pay Period</h3>
        <button class="close">&times;</button>
      </div>
      <ul id="periodsList">
        <% if (currentPeriod) { %>
            <%
            // CRITICAL FIX: Create properly scoped variables for current period
            const currentPeriodId = currentPeriod._id;
            const currentPeriodSalary = Number(currentPeriod.basicSalary || 0);
            const currentPeriodStartDate = currentPeriod.startDate;
            const currentPeriodEndDate = currentPeriod.endDate;
            const currentPeriodFinalized = currentPeriod.isFinalized;
            %>
            <%
            // Use BusinessDate fields for current period display
            const currentPeriodStartDateBusiness = currentPeriod.startDateBusiness || moment.utc(currentPeriodStartDate).format('YYYY-MM-DD');
            const currentPeriodEndDateBusiness = currentPeriod.endDateBusiness || moment.utc(currentPeriodEndDate).format('YYYY-MM-DD');
            %>
            <li class="period-item current" data-period="<%= currentPeriodEndDateBusiness %>">
                <div class="period-info">
                    <i class="fa fa-calendar-check-o" aria-hidden="true"></i>
                    <span class="period-dates">
                        <%= moment(currentPeriodStartDateBusiness).format('DD/MM/YYYY') %> - <%= moment(currentPeriodEndDateBusiness).format('DD/MM/YYYY') %>
                    </span>
                    <span class="period-status current">Current Period</span>
                </div>
                <div class="period-details">
                    <span class="salary" data-period-id="<%= currentPeriodId %>" data-salary="<%= currentPeriodSalary %>">R <%= currentPeriodSalary.toFixed(2) %></span>
                    <% if (!currentPeriodFinalized) { %>
                        <span class="status pending">Pending</span>
                    <% } %>
                </div>
            </li>
        <% } %>

        <%
        // SIMPLE FIX: Use only database periods - no business rule engine
        let allPeriods = [];

        // Add database periods only (payPeriods)
        if (payPeriods && payPeriods.length > 0) {
            allPeriods = [...payPeriods];
        }

        if (allPeriods && allPeriods.length > 0) { %>
            <%
            // BusinessDate Implementation: Show only database periods in the modal
            // Filter out the current period to avoid duplication, then sort by BusinessDate descending
            const otherPeriods = allPeriods
                .filter(period => {
                    const periodId = period._id ? period._id.toString() : '';
                    const currentId = currentPeriod ? currentPeriod._id.toString() : '';
                    return periodId !== currentId;
                })
                .sort((a, b) => {
                    // Use BusinessDate fields for sorting, fallback to legacy Date fields
                    const aEndDate = a.endDateBusiness || moment.utc(a.endDate).format('YYYY-MM-DD');
                    const bEndDate = b.endDateBusiness || moment.utc(b.endDate).format('YYYY-MM-DD');
                    return bEndDate.localeCompare(aEndDate); // String comparison for YYYY-MM-DD
                });

            // Use traditional for loop with proper variable scoping
            for (let i = 0; i < otherPeriods.length; i++) {
                const period = otherPeriods[i];
                // Create local variables to ensure proper scoping
                const periodId = period._id;
                const periodSalary = Number(period.basicSalary || 0);
                // Use BusinessDate fields for timezone-independent display
                const periodStartDateBusiness = period.startDateBusiness || moment.utc(period.startDate).format('YYYY-MM-DD');
                const periodEndDateBusiness = period.endDateBusiness || moment.utc(period.endDate).format('YYYY-MM-DD');
                const periodFinalized = period.isFinalized;
            %>
                <li class="period-item <%= periodFinalized ? 'finalized' : 'unfinalized' %>"
                    data-period="<%= periodEndDateBusiness %>"
                    title="Database Period (BusinessDate)">
                    <div class="period-info">
                        <i class="fa <%= periodFinalized ? 'fa-lock' : 'fa-calendar-o' %>" aria-hidden="true"></i>
                        <span class="period-dates">
                            <%= moment(periodStartDateBusiness).format('DD/MM/YYYY') %> - <%= moment(periodEndDateBusiness).format('DD/MM/YYYY') %>
                        </span>
                        <span class="period-status <%= periodFinalized ? 'finalized' : 'pending' %>">
                            <%= periodFinalized ? 'Finalized' : 'Pending' %>
                        </span>
                    </div>
                    <div class="period-details">
                        <span class="salary" data-period-id="<%= periodId %>" data-salary="<%= periodSalary %>">
                            R <%= periodSalary.toFixed(2) %>
                        </span>
                        <span class="status <%= periodFinalized ? 'completed' : 'pending' %>">
                            <%= periodFinalized ? 'Completed' : 'Pending' %>
                        </span>
                    </div>
                </li>
            <% } %>
        <% } else { %>
            <li class="no-periods">No periods available</li>
        <% } %>
      </ul>
      <div class="modal-footer">
        <a href="/clients/<%= companyCode %>/employeeProfile/<%= employee._id %>/addPayslip" class="btn btn-primary">
            <i class="fa fa-plus"></i> Manually Add Payslip
        </a>
      </div>
    </div>
  </div>


  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Period Modal Salary Debug - BusinessDate Implementation
      console.log('🔍 Period Modal Salary Debug (BusinessDate):', {
        currentPeriodSalary: '<%= currentPeriod ? currentPeriod.basicSalary : "N/A" %>',
        payPeriodsCount: '<%= payPeriods ? payPeriods.length : 0 %>',
        payPeriodsData: [
          <% if (payPeriods && payPeriods.length > 0) { %>
            <%
            // Use proper scoping to prevent closure issues in debugging too
            for (let debugIndex = 0; debugIndex < payPeriods.length; debugIndex++) {
              const debugPeriod = payPeriods[debugIndex];
              const debugStartDateBusiness = debugPeriod.startDateBusiness || moment.utc(debugPeriod.startDate).format('YYYY-MM-DD');
              const debugEndDateBusiness = debugPeriod.endDateBusiness || moment.utc(debugPeriod.endDate).format('YYYY-MM-DD');
            %>
              {
                id: '<%= debugPeriod._id %>',
                startDateBusiness: '<%= debugStartDateBusiness %>',
                endDateBusiness: '<%= debugEndDateBusiness %>',
                legacyStartDate: '<%= debugPeriod.startDate %>',
                legacyEndDate: '<%= debugPeriod.endDate %>',
                basicSalary: <%= debugPeriod.basicSalary || 0 %>,
                isFinalized: <%= debugPeriod.isFinalized %>
              }<%= debugIndex < payPeriods.length - 1 ? ',' : '' %>
            <% } %>
          <% } %>
        ]
      });

      // Verify salary display integrity
      function verifySalaryDisplay() {
        const salaryElements = document.querySelectorAll('.salary[data-period-id]');
        console.log('🔍 Verifying salary display for', salaryElements.length, 'periods:');

        salaryElements.forEach((element, index) => {
          const periodId = element.getAttribute('data-period-id');
          const dataSalary = element.getAttribute('data-salary');
          const displayedText = element.textContent.trim();
          const displayedAmount = displayedText.replace('R ', '').replace(/,/g, '');

          console.log(`Period ${index + 1}:`, {
            periodId,
            dataSalary,
            displayedText,
            displayedAmount,
            isCorrect: parseFloat(dataSalary).toFixed(2) === parseFloat(displayedAmount).toFixed(2)
          });
        });
      }

      // Run verification after DOM is loaded
      setTimeout(verifySalaryDisplay, 100);

      // Payroll Actions Card Button Debugging
      console.log('🔧 Payroll Actions Card Debug:', {
        finalizeButton: document.getElementById('finaliseButton'),
        downloadButton: document.querySelector('.downloadPayslip button'),
        companyCode: document.body.dataset.companyCode,
        actionCard: document.querySelector('.payroll-actions-card')
      });

      // Enhanced finalization state management
      const finalizeBtn = document.getElementById('finaliseButton');
      const downloadBtn = document.querySelector('.downloadPayslip button');
      const contentSection = document.getElementById('content-section');
      const isFinalized = contentSection && contentSection.classList.contains('period-finalized');

      if (finalizeBtn) {
        console.log('✅ Finalize button found and ready');

        // Check if this is a finalize button (not unfinalize)
        if (!finalizeBtn.classList.contains('unfinalize-button')) {
          console.log('🔍 Adding finalize validation feedback');

          finalizeBtn.addEventListener('click', async function(e) {
            e.preventDefault();

            console.log('🎯 Finalize button clicked', {
              disabled: this.disabled,
              form: this.form,
              action: this.form?.action
            });

            const employeeId = '<%= employee._id %>';
            const periodEndDate = '<%= currentPeriod ? (currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format("YYYY-MM-DD")) : "" %>';
            const companyCode = '<%= company.companyCode %>';

            // Show loading state
            this.disabled = true;
            this.innerHTML = '<i class="ph ph-spinner ph-spin"></i> Checking...';

            try {
              // Check finalization eligibility
              const response = await fetch(`/clients/${companyCode}/employeeProfile/${employeeId}/period/${periodEndDate}/can-finalize`);
              const data = await response.json();

              if (data.success && data.canFinalize) {
                // Validation passed, proceed with form submission
                this.innerHTML = '<i class="ph ph-check-circle"></i> Finalizing...';
                this.form.submit();
              } else {
                // Show validation errors
                this.disabled = false;
                this.innerHTML = '<i class="ph ph-check-circle"></i> Finalise Period';

                const errors = data.errors || ['Cannot finalize period'];
                showToast('error', 'Cannot Finalize', errors.join('; '));
              }
            } catch (error) {
              console.error('Error checking finalization:', error);
              this.disabled = false;
              this.innerHTML = '<i class="ph ph-check-circle"></i> Finalise Period';
              showToast('error', 'Error', 'Error checking finalization eligibility. Please try again.');
            }
          });
        } else {
          // Just log for unfinalize buttons (handled separately)
          finalizeBtn.addEventListener('click', function(e) {
            console.log('🎯 Unfinalize button clicked', {
              disabled: this.disabled,
              form: this.form,
              action: this.form?.action
            });
          });
        }
      } else {
        console.error('❌ Finalize button not found');
      }

      if (downloadBtn) {
        console.log('✅ Download button found and ready');
        downloadBtn.addEventListener('click', function(e) {
          console.log('🎯 Download button clicked', {
            onclick: this.getAttribute('onclick')
          });
        });
      } else {
        console.error('❌ Download button not found');
      }

      // Add user feedback for finalized period interactions
      if (isFinalized) {
        console.log('📋 Period is finalized - adding interaction feedback');

        // Add click handlers for disabled elements to show feedback
        const disabledLinks = document.querySelectorAll('.period-finalized .input-link, .period-finalized .placeholder-link');
        const disabledButtons = document.querySelectorAll('.period-finalized .add-button');

        disabledLinks.forEach(link => {
          link.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            showFinalizationFeedback('This form is disabled because the period is finalized. Use "Unfinalize Period" to make changes.');
          });
        });

        disabledButtons.forEach(button => {
          button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            showFinalizationFeedback('Cannot add new items to a finalized period. Use "Unfinalize Period" to make changes.');
          });
        });
      }

      // Function to show finalization feedback
      function showFinalizationFeedback(message) {
        // Create or update feedback tooltip
        let tooltip = document.getElementById('finalization-tooltip');
        if (!tooltip) {
          tooltip = document.createElement('div');
          tooltip.id = 'finalization-tooltip';
          tooltip.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1f2937;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            max-width: 400px;
            text-align: center;
            font-size: 0.875rem;
            line-height: 1.5;
            opacity: 0;
            transition: opacity 0.3s ease;
          `;
          document.body.appendChild(tooltip);
        }

        tooltip.textContent = message;
        tooltip.style.opacity = '1';

        // Auto-hide after 3 seconds
        setTimeout(() => {
          tooltip.style.opacity = '0';
        }, 3000);
      }

      // Enhanced unfinalize button validation feedback
      if (finalizeBtn && finalizeBtn.classList.contains('unfinalize-button')) {
        console.log('🔄 Adding unfinalize validation feedback');

        finalizeBtn.addEventListener('click', async function(e) {
          e.preventDefault();

          const employeeId = '<%= employee._id %>';
          const periodEndDate = '<%= currentPeriod ? (currentPeriod.endDateBusiness || moment.utc(currentPeriod.endDate).format("YYYY-MM-DD")) : "" %>';
          const companyCode = '<%= company.companyCode %>';

          // Show loading state
          this.disabled = true;
          this.innerHTML = '<i class="ph ph-spinner ph-spin"></i> Checking...';

          try {
            // Check unfinalization eligibility
            const response = await fetch(`/clients/${companyCode}/employeeProfile/${employeeId}/period/${periodEndDate}/can-unfinalize`);
            const data = await response.json();

            if (data.success && data.canUnfinalize) {
              // Show confirmation dialog
              const confirmed = await showConfirmation({
                title: 'Unfinalize Period',
                icon: 'ph-arrow-counter-clockwise',
                message: `Are you sure you want to unfinalize the period ending ${periodEndDate}? This will allow modifications to the payroll data.`,
                details: data.warnings && data.warnings.length > 0 ?
                  `<h4>Please note:</h4><ul>${data.warnings.map(w => `<li>${w}</li>`).join('')}</ul>` : null,
                confirmText: 'Unfinalize Period',
                confirmType: 'warning',
                cancelText: 'Cancel'
              });

              if (confirmed) {
                // Proceed with form submission
                this.innerHTML = '<i class="ph ph-arrow-counter-clockwise"></i> Unfinalizing...';
                this.form.submit();
              } else {
                // Reset button
                this.disabled = false;
                this.innerHTML = '<i class="ph ph-arrow-counter-clockwise"></i> Unfinalize Period';
              }
            } else {
              // Show validation errors
              this.disabled = false;
              this.innerHTML = '<i class="ph ph-arrow-counter-clockwise"></i> Unfinalize Period';

              const errors = data.errors || ['Cannot unfinalize period'];
              showToast('error', 'Cannot Unfinalize', errors.join('; '));
            }
          } catch (error) {
            console.error('Error checking unfinalization:', error);
            this.disabled = false;
            this.innerHTML = '<i class="ph ph-arrow-counter-clockwise"></i> Unfinalize Period';
            showToast('error', 'Error', 'Error checking unfinalization eligibility. Please try again.');
          }
        });
      }

      // Toast Notification System
      window.showToast = function(type, title, message, duration = 5000) {
        const container = document.getElementById('toast-container');
        if (!container) return;

        // Safety checks for undefined values
        if (!type || !title) {
          console.warn('showToast: type and title are required');
          return;
        }

        if (!message || message === 'undefined' || message.trim() === '') {
          console.warn('showToast: skipping toast with empty/undefined message');
          return;
        }

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const iconMap = {
          success: 'ph-check-circle',
          error: 'ph-x-circle',
          warning: 'ph-warning-circle',
          info: 'ph-info'
        };

        // Escape HTML to prevent XSS and handle special characters
        const safeTitle = String(title).replace(/[&<>"']/g, function(match) {
          const escapeMap = { '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;' };
          return escapeMap[match];
        });

        const safeMessage = String(message).replace(/[&<>"']/g, function(match) {
          const escapeMap = { '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;' };
          return escapeMap[match];
        });

        toast.innerHTML = `
          <i class="ph ${iconMap[type] || 'ph-info'} toast-icon"></i>
          <div class="toast-content">
            <div class="toast-title">${safeTitle}</div>
            <div class="toast-message">${safeMessage}</div>
          </div>
          <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="ph ph-x"></i>
          </button>
        `;

        container.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto-remove after duration
        setTimeout(() => {
          toast.classList.remove('show');
          setTimeout(() => toast.remove(), 300);
        }, duration);
      };

      // Enhanced finalization feedback using toast
      function showFinalizationFeedback(message) {
        showToast('warning', 'Period Finalized', message);
      }

      // Confirmation Dialog System
      window.showConfirmation = function(options) {
        return new Promise((resolve) => {
          const overlay = document.getElementById('confirmation-overlay');
          const title = document.getElementById('confirmation-title');
          const message = document.getElementById('confirmation-message');
          const details = document.getElementById('confirmation-details');
          const cancelBtn = document.getElementById('confirmation-cancel');
          const confirmBtn = document.getElementById('confirmation-confirm');

          // Set content
          title.innerHTML = `<i class="ph ${options.icon || 'ph-warning-circle'}"></i> ${options.title}`;
          message.textContent = options.message;

          if (options.details) {
            details.innerHTML = options.details;
            details.style.display = 'block';
          } else {
            details.style.display = 'none';
          }

          // Set button styles and text
          confirmBtn.textContent = options.confirmText || 'Confirm';
          confirmBtn.className = `confirmation-btn ${options.confirmType || 'confirm'}`;
          cancelBtn.textContent = options.cancelText || 'Cancel';

          // Show dialog
          overlay.classList.add('show');

          // Handle events
          const handleCancel = () => {
            overlay.classList.remove('show');
            cleanup();
            resolve(false);
          };

          const handleConfirm = () => {
            overlay.classList.remove('show');
            cleanup();
            resolve(true);
          };

          const cleanup = () => {
            cancelBtn.removeEventListener('click', handleCancel);
            confirmBtn.removeEventListener('click', handleConfirm);
            overlay.removeEventListener('click', handleOverlayClick);
          };

          const handleOverlayClick = (e) => {
            if (e.target === overlay) {
              handleCancel();
            }
          };

          cancelBtn.addEventListener('click', handleCancel);
          confirmBtn.addEventListener('click', handleConfirm);
          overlay.addEventListener('click', handleOverlayClick);
        });
      };

      // Show flash messages as toasts
      <% if (typeof messages !== 'undefined') { %>
        <% if (messages.success && messages.success.length > 0) { %>
          <% messages.success.forEach(function(msg) { %>
            <% if (msg && msg.trim()) { %>
              showToast('success', 'Success', '<%= msg %>');
            <% } %>
          <% }); %>
        <% } %>
        <% if (messages.error && messages.error.length > 0) { %>
          <% messages.error.forEach(function(msg) { %>
            <% if (msg && msg.trim()) { %>
              showToast('error', 'Error', '<%= msg %>');
            <% } %>
          <% }); %>
        <% } %>
        <% if (messages.warning && messages.warning.length > 0) { %>
          <% messages.warning.forEach(function(msg) { %>
            <% if (msg && msg.trim()) { %>
              showToast('warning', 'Warning', '<%= msg %>');
            <% } %>
          <% }); %>
        <% } %>
        <% if (messages.info && messages.info.length > 0) { %>
          <% messages.info.forEach(function(msg) { %>
            <% if (msg && msg.trim()) { %>
              showToast('info', 'Info', '<%= msg %>');
            <% } %>
          <% }); %>
        <% } %>
      <% } %>

      // Logging for Deductions Section
      const deductionRows = document.querySelectorAll('.deductions .row');
      console.log('Deductions Section Debug:', Array.from(deductionRows).map(row => {
        const label = row.querySelector('label');
        const value = row.querySelector('span');
        return {
          label: label ? label.textContent.trim() : 'Unknown Label',
          value: value ? value.textContent.trim() : 'No Value'
        };
      }));

      // Logging for Summary Section
      const summaryRows = document.querySelectorAll('.calc-group.summary .row');
      console.log('Summary Section Debug:', Array.from(summaryRows).map(row => {
        const label = row.querySelector('label');
        const value = row.querySelector('span');
        return {
          label: label ? label.textContent.trim() : 'Unknown Label',
          value: value ? value.textContent.trim() : 'No Value'
        };
      }));

      // Logging for Payroll Totals
      const payrollTotalsSection = document.querySelector('.calc-group.summary');
      if (payrollTotalsSection) {
        console.log('Payroll Totals Section Full HTML:', payrollTotalsSection.innerHTML);
      }

      // Log PAYE and UIF values using safer selectors - find by label text content
      const findRowByLabelText = (selector, text) => {
        const rows = document.querySelectorAll(selector);
        for (const row of rows) {
          const label = row.querySelector('label');
          if (label && label.textContent.trim().includes(text)) {
            return row;
          }
        }
        return null;
      };

      const payeRow = findRowByLabelText('.deductions .row', 'PAYE Tax');
      const uifRow = findRowByLabelText('.deductions .row', 'UIF');

      console.log('PAYE and UIF Specific Debug:', {
        payeValue: payeRow ? payeRow.querySelector('span')?.textContent.trim() : 'Not Found',
        uifValue: uifRow ? uifRow.querySelector('span')?.textContent.trim() : 'Not Found'
      });
    });
  </script>
  <script>
      document.addEventListener('DOMContentLoaded', function() {
          const modal = document.getElementById('monthModal');
          const btn = document.getElementById('selectMonthButton');
          const span = document.getElementsByClassName('close')[0];
          const periodsList = document.getElementById('periodsList');

          // Show modal with animation
          if (btn) {
              btn.onclick = function() {
                  modal.style.display = 'block';
                  document.body.style.overflow = 'hidden';
              }
          }

          // Close modal with animation
          function closeModal() {
              modal.style.opacity = '0';
              setTimeout(() => {
                  modal.style.display = 'none';
                  modal.style.opacity = '1';
                  document.body.style.overflow = 'auto';
              }, 300);
          }

          if (span) {
              span.onclick = closeModal;
          }

          // Close when clicking outside
          window.onclick = function(event) {
              if (event.target == modal) {
                  closeModal();
              }
          }

          // Handle period selection
          if (periodsList) {
              periodsList.addEventListener('click', function(e) {
                  const periodItem = e.target.closest('.period-item');
                  if (!periodItem) return;

                  const periodDate = periodItem.getAttribute('data-period');
                  const employeeId = btn.getAttribute('data-employee-id');
                  const isFinalized = periodItem.classList.contains('finalized');

                  // Update button text
                  const periodDates = periodItem.querySelector('.period-dates').textContent;
                  const status = isFinalized ? 'Finalized' : 'Pending';
                  
                  btn.innerHTML = `
                      <div class="period-display">
                          ${periodDates}
                          <span class="status-indicator ${isFinalized ? 'completed' : 'pending'}">${status}</span>
                      </div>
                      <ion-icon name="chevron-down-outline"></ion-icon>
                  `;

                  // Navigate to selected period
                  window.location.href = `/clients/<%= companyCode %>/employeeProfile/${employeeId}?selectedMonth=${periodDate}&viewFinalized=${isFinalized}`;
              });
          }

          // Add hover effect
          const periodItems = document.querySelectorAll('.period-item');
          periodItems.forEach(item => {
              item.addEventListener('mouseenter', function() {
                  this.style.transform = 'translateX(5px)';
              });
              item.addEventListener('mouseleave', function() {
                  this.style.transform = 'translateX(0)';
              });
          });
      });

      // Remove duplicate function - using the one from employeeProfile.js

      // Wrapper function to safely handle delete employee action
      function handleDeleteEmployee(employeeId) {
        if (typeof window.confirmDeleteEmployee === 'function') {
          window.confirmDeleteEmployee(employeeId);
        } else {
          console.error('confirmDeleteEmployee function not available. Please refresh the page.');
          alert('Delete function not available. Please refresh the page and try again.');
        }
      }

      // Handle finalized period input links
      document.addEventListener('DOMContentLoaded', function() {
          const isFinalized = <%= currentPeriod?.isFinalized ? 'true' : 'false' %>;

          if (isFinalized) {
              // Find all input links in Regular Inputs and Payslip Inputs cards that haven't been manually updated
              const inputCards = document.querySelectorAll('.input-card');

              inputCards.forEach(card => {
                  // Skip if already has finalized-period class (manually updated)
                  if (card.classList.contains('finalized-period')) return;

                  // Find all anchor tags that are input links
                  const inputLinks = card.querySelectorAll('a[href*="/employeeProfile/"]');

                  inputLinks.forEach(link => {
                      // Skip if already processed
                      if (link.classList.contains('finalized-input') || link.parentElement.classList.contains('finalized-input')) return;

                      // Convert link to disabled state
                      const linkText = link.textContent.trim();
                      const linkHTML = link.innerHTML;

                      // Create disabled span
                      const disabledSpan = document.createElement('span');
                      disabledSpan.className = 'finalized-link';
                      disabledSpan.innerHTML = linkHTML;

                      // Replace the link
                      link.parentNode.replaceChild(disabledSpan, link);
                  });

                  // Handle input-link class elements (full clickable items)
                  const fullInputLinks = card.querySelectorAll('.input-link:not(.finalized-input)');

                  fullInputLinks.forEach(link => {
                      link.classList.add('finalized-input');
                      link.style.cursor = 'not-allowed';
                      link.style.pointerEvents = 'none';
                      link.style.opacity = '0.7';

                      // Update "Click to add" text
                      const emptyValues = link.querySelectorAll('.item-value.empty');
                      emptyValues.forEach(value => {
                          if (value.textContent.includes('Click to add')) {
                              value.textContent = 'Not configured';
                          }
                      });
                  });
              });
          }
      });

      // Make function globally available
      window.handleDeleteEmployee = handleDeleteEmployee;
  </script>
  <script src="/js/employeeProfile.js"></script>
  <script src="/js/employeeActions.js"></script>

  <!-- CRITICAL FIX: Frontend debugging for data synchronization -->
  <script>
  document.addEventListener('DOMContentLoaded', function() {
      console.log('=== FRONTEND DATA DEBUG ===');
      console.log('Template Data Received:', {
          basicSalary: '<%= basicSalary || "undefined" %>',
          currentPeriodBasicSalary: '<%= currentPeriod?.basicSalary || "undefined" %>',
          payrollBasicSalary: '<%= payroll?.basicSalary || "undefined" %>',
          totalIncome: '<%= totalIncome || "undefined" %>',
          selectedMonth: '<%= typeof selectedMonth !== "undefined" && selectedMonth ? moment.utc(selectedMonth).format('YYYY-MM-DD') : "undefined" %>',
          timestamp: new Date().toISOString()
      });

      // Log actual DOM values
      const basicSalaryElement = document.querySelector('.item-value');
      if (basicSalaryElement) {
          console.log('DOM Basic Salary Value:', basicSalaryElement.textContent.trim());
      }
  });
  </script>

  <%- include('partials/mobile-bottom-nav', { req: { path: `/clients/${company.companyCode}/employeeManagement` }, company: company }) %>
</body>
</html>

<script>
  console.log("🎂 Employee Age Debug:", {
    "Date of Birth": "<%= employee.dob ? moment(employee.dob).format('YYYY-MM-DD') : 'N/A' %>",
    "Calculated Age": "<%= employeeAge %>",
    "Employee ID": "<%= employee._id %>"
  });
</script>
