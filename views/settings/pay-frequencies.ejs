<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pay Frequencies - <%= company.name %></title>

    <!-- CSRF Token -->
    <meta name="csrf-token" content="<%= typeof csrfToken !== 'undefined' ? csrfToken : '' %>">

    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Toast Notifications -->
    <script src="/js/toast-notifications.js"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/settings.css" />
    <link rel="stylesheet" href="/css/accounting-settings.css" />
    <link rel="stylesheet" href="/css/toast.css" />

    <style>
      .pay-frequencies-container {
        padding: 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .add-frequency-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: #6366f1;
        color: white;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        text-decoration: none;
        margin-bottom: 2rem;
        transition: all 0.2s ease;
      }

      .add-frequency-button:hover {
        background: #818cf8;
      }

      .frequency-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
      }

      .frequency-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
      }

      .frequency-card h3 {
        color: #1e293b;
        font-size: 1.125rem;
        font-weight: 600;
        margin: 0 0 1rem 0;
      }

      .frequency-detail {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        color: #64748b;
      }

      .frequency-detail label {
        font-size: 0.875rem;
        font-weight: 500;
        width: 120px;
      }

      .frequency-detail span {
        color: #1e293b;
      }

      .card-actions {
        display: flex;
        gap: 0.75rem;
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 1px solid #e2e8f0;
      }

      .action-button {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.2s ease;
      }

      .edit-button {
        color: #6366f1;
        background: rgba(99, 102, 241, 0.1);
      }

      .edit-button:hover {
        background: rgba(99, 102, 241, 0.2);
      }

      .delete-button {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
        border: none;
        cursor: pointer;
      }

      .delete-button:hover {
        background: rgba(239, 68, 68, 0.2);
      }

      .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
      }

      .empty-state i {
        font-size: 3rem;
        color: #64748b;
        margin-bottom: 1rem;
      }

      .empty-state p {
        color: #64748b;
        font-size: 0.875rem;
      }

      .alert {
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
      }

      .alert-success {
        background: #f0fdf4;
        border: 1px solid #bbf7d0;
        color: #166534;
      }

      .alert-danger {
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #991b1b;
      }

      @media (max-width: 768px) {
        .pay-frequencies-container {
          padding: 1rem;
        }

        .frequency-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>

  <body>
    <!-- Toast container for notifications -->
    <div id="toast-container" class="toast-container"></div>

    <div class="layout-wrapper">
      <%- include('../partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('../partials/header', { user: user, company: company, currentPage: 'settings' }) %>

        <main class="main-container" style="margin-top: 6.5rem;">

      <!-- Tabs Section -->
      <div id="tabs-section">
        <div class="tab-row main-tabs">
          <a
            href="/clients/<%= company.companyCode %>/settings/accounting"
            class="tab-button"
          >
            <i class="ph ph-calculator"></i>
            <span>Accounting</span>
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/employee"
            class="tab-button"
          >
            <i class="ph ph-users"></i>
            <span>Employee</span>
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/payroll"
            class="tab-button <%= activeTab === 'payroll' ? 'active' : '' %>"
          >
            <i class="ph ph-money"></i>
            <span>Payroll</span>
          </a>
          <a
            href="/clients/<%= company.companyCode %>/settings/other"
            class="tab-button"
          >
            <i class="ph ph-gear"></i>
            <span>Other</span>
          </a>
        </div>
      </div>

      <div class="pay-frequencies-container">
        <% if (messages.success) { %>
        <div class="alert alert-success"><%= messages.success %></div>
        <% } %> <% if (messages.error) { %>
        <div class="alert alert-danger"><%= messages.error %></div>
        <% } %>

        <a href="/pay-frequency/new" class="add-frequency-button">
          <i class="ph ph-plus"></i>
          Add New Pay Frequency
        </a>

        <% if (payFrequencies.length > 0) { %>
        <div class="frequency-grid">
          <% payFrequencies.forEach(function(payFrequency) { %>
          <div class="frequency-card">
            <h3><%= payFrequency.name %></h3>

            <div class="frequency-detail">
              <label>Frequency:</label>
              <span><%= payFrequency.frequency %></span>
            </div>

            <div class="frequency-detail">
              <label>Last Day:</label>
              <span><%= payFrequency.lastDayOfPeriod %></span>
            </div>

            <% if (payFrequency.frequency === 'bi-weekly') { %>
            <div class="frequency-detail">
              <label>Interim Day:</label>
              <span><%= payFrequency.interimDay %></span>
            </div>
            <% } %>

            <div class="card-actions">
              <a
                href="/pay-frequency/edit/<%= payFrequency._id %>"
                class="action-button edit-button"
              >
                <i class="ph ph-pencil"></i>
                Edit
              </a>
              <button
                class="action-button delete-button"
                data-frequency-id="<%= payFrequency._id %>"
              >
                <i class="ph ph-trash"></i>
                Delete
              </button>
            </div>
          </div>
          <% }); %>
        </div>
        <% } else { %>
        <div class="empty-state">
          <i class="ph ph-calendar-blank ph-3x"></i>
          <p>
            No pay frequencies found. Add your first pay frequency to get
            started.
          </p>
        </div>
        <% } %>
      </div>
        </main>
      </div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Handle any flash messages
        <% if (typeof messages !== 'undefined' && messages.error) { %>
          showErrorToast('<%= messages.error %>');
        <% } %>
        <% if (typeof messages !== 'undefined' && messages.success) { %>
          showSuccessToast('<%= messages.success %>');
        <% } %>
        
        // Pay frequency deletion confirmation modal
        function showDeleteConfirmationModal(frequencyId, frequencyName) {
          // Create modal container
          const modal = document.createElement('div');
          modal.className = 'confirmation-modal';
          modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1050;
            opacity: 0;
            transition: opacity 0.3s ease;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          `;

          modal.innerHTML = `
            <div class="modal-content" style="
              background-color: white;
              padding: 2rem;
              border-radius: 8px;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
              width: 90%;
              max-width: 500px;
              text-align: center;
              position: relative;
              transform: translateY(20px);
              transition: transform 0.3s ease;
              max-height: 90vh;
              overflow-y: auto;
              margin: 0 1rem;
            ">
              <i class="ph ph-warning-circle" style="
                font-size: 3rem;
                color: #f59e0b;
                margin-bottom: 1rem;
                display: inline-block;
              "></i>
              <h3 style="margin-bottom: 1rem; font-size: 1.5rem; color: #111827; font-weight: 600;">Delete Pay Frequency</h3>
              <p style="margin-bottom: 2rem; color: #4b5563; line-height: 1.5;">
                Are you sure you want to delete the pay frequency "<strong>${frequencyName}</strong>"? This action cannot be undone.
              </p>
              <div class="modal-actions" style="
                display: flex;
                justify-content: center;
                gap: 1rem;
                flex-wrap: wrap;
              ">
                <button id="cancelDeleteBtn" class="btn-secondary" style="
                  padding: 0.75rem 1.5rem;
                  background-color: #e5e7eb;
                  color: #374151;
                  border: none;
                  border-radius: 0.375rem;
                  cursor: pointer;
                  font-weight: 500;
                  transition: background-color 0.2s;
                  min-width: 100px;
                  font-family: 'Inter', sans-serif;
                ">Cancel</button>
                <button id="confirmDeleteBtn" class="btn-danger" style="
                  padding: 0.75rem 1.5rem;
                  background-color: #ef4444;
                  color: white;
                  border: none;
                  border-radius: 0.375rem;
                  cursor: pointer;
                  font-weight: 500;
                  transition: background-color 0.2s;
                  min-width: 100px;
                  font-family: 'Inter', sans-serif;
                ">Delete</button>
              </div>
            </div>
          `;

          document.body.appendChild(modal);

          // Prevent body scrolling
          const originalOverflow = document.body.style.overflow;
          document.body.style.overflow = 'hidden';

          // Trigger the animation
          setTimeout(() => {
            modal.style.opacity = '1';
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
              modalContent.style.transform = 'translateY(0)';
            }
          }, 10);

          // Add button hover effects
          const handleMouseOver = function(e) {
            if (e.target.id === 'cancelDeleteBtn') {
              e.target.style.backgroundColor = '#d1d5db';
            }
            if (e.target.id === 'confirmDeleteBtn') {
              e.target.style.backgroundColor = '#dc2626';
            }
          };

          const handleMouseOut = function(e) {
            if (e.target.id === 'cancelDeleteBtn') {
              e.target.style.backgroundColor = '#e5e7eb';
            }
            if (e.target.id === 'confirmDeleteBtn') {
              e.target.style.backgroundColor = '#ef4444';
            }
          };

          modal.addEventListener('mouseover', handleMouseOver);
          modal.addEventListener('mouseout', handleMouseOut);

          // Close modal function
          const closeModal = () => {
            // Fade out animation
            modal.style.opacity = '0';
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
              modalContent.style.transform = 'translateY(20px)';
            }

            // Restore body scrolling
            document.body.style.overflow = originalOverflow;

            // Remove after animation completes
            setTimeout(() => {
              // Remove event listeners before removing the modal
              modal.removeEventListener('mouseover', handleMouseOver);
              modal.removeEventListener('mouseout', handleMouseOut);
              modal.remove();
            }, 300);
          };

          // Close when clicking outside
          modal.addEventListener('click', function(event) {
            if (event.target === modal) {
              closeModal();
            }
          });

          // Handle escape key
          const handleEscape = function(event) {
            if (event.key === 'Escape') {
              closeModal();
              document.removeEventListener('keydown', handleEscape);
            }
          };
          document.addEventListener('keydown', handleEscape);

          // Handle cancel button
          const cancelBtn = document.getElementById('cancelDeleteBtn');
          if (cancelBtn) {
            cancelBtn.addEventListener('click', function() {
              closeModal();
              document.removeEventListener('keydown', handleEscape);
            });
          }

          // Handle confirmation
          const confirmBtn = document.getElementById('confirmDeleteBtn');
          if (confirmBtn) {
            confirmBtn.addEventListener('click', async function() {
              // Close modal first
              closeModal();
              document.removeEventListener('keydown', handleEscape);

              // Get CSRF token
              const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

              try {
                const response = await fetch(`/pay-frequency/delete/${frequencyId}`, {
                  method: 'DELETE',
                  headers: {
                    'Content-Type': 'application/json',
                    'CSRF-Token': csrfToken
                  }
                });

                const data = await response.json();

                if (response.ok) {
                  showSuccessToast('Pay frequency deleted successfully');
                  // Remove the frequency card from the UI
                  const frequencyCard = document.querySelector(`[data-frequency-id="${frequencyId}"]`)?.closest('.frequency-card');
                  if (frequencyCard) {
                    frequencyCard.remove();
                  }
                } else {
                  showErrorToast(data.error || 'Error deleting pay frequency');
                }
              } catch (error) {
                console.error('Error:', error);
                showErrorToast('Connection timeout or server error. Please try again.');
              }
            });
          }
        }

        // Add error handling for AJAX requests
        const deleteButtons = document.querySelectorAll('.delete-button');
        if (deleteButtons) {
          deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
              e.preventDefault();
              const frequencyId = this.getAttribute('data-frequency-id');
              const frequencyName = this.closest('.frequency-card').querySelector('h3').textContent.trim();
              showDeleteConfirmationModal(frequencyId, frequencyName);
            });
          });
        }
      });
    </script>
  </body>
</html>
