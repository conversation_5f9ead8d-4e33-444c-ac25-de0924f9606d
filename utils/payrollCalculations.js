const moment = require("moment-timezone");
const PayrollPeriod = require("../models/PayrollPeriod");

// Set the default timezone
const DEFAULT_TIMEZONE = "Africa/Johannesburg";

// Add these constants at the top of the file
const MEDICAL_AID_TAX_CREDITS = {
  MAIN_MEMBER: 364, // R364 per month for main member
  FIRST_DEPENDENT: 364, // R364 per month for first dependent
  ADDITIONAL_DEPENDENT: 246, // R246 per month for each additional dependent
};

// Add these tax-related constants
const TAX_BRACKETS_2024 = [
  { threshold: 0, rate: 0.18, base: 0, nextBracketThreshold: 237100 },
  { threshold: 237100, rate: 0.26, base: 42678, nextBracketThreshold: 370500 },
  { threshold: 370500, rate: 0.31, base: 77362, nextBracketThreshold: 512800 },
  { threshold: 512800, rate: 0.36, base: 121475, nextBracketThreshold: 673000 },
  { threshold: 673000, rate: 0.39, base: 179147, nextBracketThreshold: 857900 },
  { threshold: 857900, rate: 0.41, base: 251258, nextBracketThreshold: 1817000 },
  { threshold: 1817000, rate: 0.45, base: 644489, nextBracketThreshold: null }
];

// Travel Allowance Calculation Constants
const SARS_KILOMETER_RATE = 4.48; // 2024 rate
const TRAVEL_ALLOWANCE_TAXABLE_PERCENTAGE = 0.80; // 80% taxable

function isProratedMonth(employee, payrollDate) {
  const doaDate = moment.tz(employee.doa, DEFAULT_TIMEZONE).startOf("day");
  const payrollStartDate = moment
    .tz(payrollDate, DEFAULT_TIMEZONE)
    .startOf("day");

  let payFrequency = "monthly"; // Default to monthly

  if (employee && employee.payFrequency) {
    if (typeof employee.payFrequency === "string") {
      payFrequency = employee.payFrequency.toLowerCase();
    } else if (
      typeof employee.payFrequency === "object" &&
      employee.payFrequency.frequency
    ) {
      payFrequency = employee.payFrequency.frequency.toLowerCase();
    }
  }

  switch (payFrequency) {
    case "weekly":
      return doaDate.isSame(payrollStartDate, "week") && doaDate.day() !== 0;
    case "bi-weekly":
    case "bi weekly":
      const weekStart = payrollStartDate.clone().startOf("week");
      const weekEnd = weekStart.clone().add(13, "days");
      return (
        doaDate.isBetween(weekStart, weekEnd, null, "[]") &&
        !doaDate.isSame(weekStart)
      );
    case "monthly":
    default:
      return doaDate.isSame(payrollStartDate, "month") && doaDate.date() > 1;
  }
}

// Utility function to calculate age
function calculateAge(dob) {
  if (!dob) return 0;
  const birthDate = moment(dob);
  const today = moment();
  let age = today.diff(birthDate, 'years');
  
  // Adjust age if birthday hasn't occurred this year
  const birthdayThisYear = moment(dob).year(today.year());
  if (today.isBefore(birthdayThisYear)) {
    age--;
  }
  
  return age;
}

function calculateProratedSalary(
  doa,
  basicSalary,
  employee,
  periodStart,
  periodEnd
) {

  const startDate = moment.tz(doa, DEFAULT_TIMEZONE).startOf("day");
  const periodStartDate = moment
    .tz(periodStart, DEFAULT_TIMEZONE)
    .startOf("day");
  const periodEndDate = moment.tz(periodEnd, DEFAULT_TIMEZONE).endOf("day");
  const firstConfiguredEnd = moment.tz(
    employee.payFrequency.firstPayrollPeriodEndDate,
    DEFAULT_TIMEZONE
  );

  // Determine if this is the first period that includes DOA
  const isFirstPeriodWithDOA = startDate.isBetween(
    periodStartDate,
    periodEndDate,
    "day",
    "[]"
  );


  // Calculate worked days based on frequency
  let totalDaysInPeriod;
  let workedDays;

  switch (employee.payFrequency.frequency) {
    case "weekly":
      // Weekly periods should be 7 days
      totalDaysInPeriod = 7;
      if (isFirstPeriodWithDOA) {
        // For first period, only count days from DOA to end of week
        workedDays = periodEndDate.diff(startDate, "days") + 1;
      } else {
        workedDays = periodEndDate.diff(periodStartDate, "days") + 1;
      }
      break;
    case "biweekly":
      // Biweekly periods should be 14 days
      totalDaysInPeriod = 14;
      if (isFirstPeriodWithDOA) {
        workedDays = periodEndDate.diff(startDate, "days") + 1;
      } else {
        workedDays = periodEndDate.diff(periodStartDate, "days") + 1;
      }
      break;
    case "monthly":
      // Monthly periods use calendar month days
      totalDaysInPeriod = periodEndDate.diff(periodStartDate, "days") + 1;
      if (isFirstPeriodWithDOA) {
        workedDays = periodEndDate.diff(startDate, "days") + 1;
      } else {
        workedDays = totalDaysInPeriod;
      }
      break;
    default:
      totalDaysInPeriod = periodEndDate.diff(periodStartDate, "days") + 1;
      workedDays = totalDaysInPeriod;
  }

  // Adjust for future employees
  if (startDate.isAfter(periodEndDate)) {
    workedDays = 0;
  }

  // Calculate prorated percentage
  const proratedPercentage = (workedDays / totalDaysInPeriod) * 100;

  // Calculate period salary based on frequency
  let periodSalary = basicSalary; // Already in correct frequency

  // Calculate prorated salary
  const proratedSalary = (periodSalary * proratedPercentage) / 100;

  // Calculate annual salary
  const annualSalary =
    basicSalary *
    (employee.payFrequency.frequency === "monthly"
      ? 12
      : employee.payFrequency.frequency === "weekly"
      ? 52
      : 26);


  return {
    proratedPercentage: proratedPercentage, // Keep full precision for calculations
    proratedSalary: proratedSalary, // Keep full precision for calculations
    fullPeriodSalary: periodSalary, // Keep full precision for calculations
    workedDays,
    totalDaysInPeriod,
    frequency: employee.payFrequency.frequency,
    annualSalary,
    isFirstPeriodWithDOA,
    // Add formatted versions for display purposes only
    proratedPercentageFormatted: proratedPercentage.toFixed(2),
    proratedSalaryFormatted: proratedSalary.toFixed(2),
    fullPeriodSalaryFormatted: periodSalary.toFixed(2),
  };
}

function calculateProratedPercentage(startDate, endDate) {
  const totalDays = endDate.diff(startDate, "days") + 1;
  const workedDays = endDate.diff(startDate, "days") + 1;
  return (workedDays / totalDays) * 100;
}

function calculateSalaryForPayPeriod(basicSalary, payFrequency) {
  // basicSalary is already the amount for the given frequency, so we return it as is
  return basicSalary;
}

function calculateCompanyCarBenefit(carDetails) {
  if (!carDetails || !carDetails.deemedValue) return 0;

  const { deemedValue, includesMaintenancePlan, taxablePercentage } =
    carDetails;

  let basePercentage = includesMaintenancePlan ? 0.0325 : 0.035;
  let monthlyBenefit = deemedValue * basePercentage;

  switch (taxablePercentage) {
    case "20%":
      monthlyBenefit *= 0.2;
      break;
    case "80%":
      monthlyBenefit *= 0.8;
      break;
    case "100%":
    default:
      break;
  }

  return monthlyBenefit;
}

function calculateCompanyCarUnderOperatingLeaseBenefit(carDetails) {
  if (!carDetails || !carDetails.amount) return 0;

  const { amount, taxablePercentage } = carDetails;

  let monthlyBenefit = amount;

  switch (taxablePercentage) {
    case "20%":
      monthlyBenefit *= 0.2;
      break;
    case "80%":
      monthlyBenefit *= 0.8;
      break;
    case "100%":
    default:
      break;
  }

  return monthlyBenefit;
}

function calculateIncomeProtectionBenefit(incomeProtection) {
  if (!incomeProtection) return 0;

  let taxableBenefit = 0;

  if (incomeProtection.employerOwnsPolicy) {
    taxableBenefit += incomeProtection.amountPaidByEmployer || 0;
  }

  return taxableBenefit;
}

function calculateRetirementAnnuityFundBenefit(retirementAnnuityFund) {
  if (!retirementAnnuityFund) return 0;
  return retirementAnnuityFund.employeeContribution || 0;
}

function calculateMedicalAidCredit(
  members,
  frequency = "monthly",
  age = 0,
  isDisabled = false
) {

  if (!members || members < 1) return 0;

  let monthlyCredit = 0;

  // Main member credit
  monthlyCredit += MEDICAL_AID_TAX_CREDITS.MAIN_MEMBER;

  // First dependent credit (if there are dependents)
  if (members >= 2) {
    monthlyCredit += MEDICAL_AID_TAX_CREDITS.FIRST_DEPENDENT;
  }

  // Additional dependents credit
  if (members > 2) {
    const additionalDependents = members - 2;
    monthlyCredit +=
      MEDICAL_AID_TAX_CREDITS.ADDITIONAL_DEPENDENT * additionalDependents;
  }

  // Convert monthly credit to the correct frequency
  let periodCredit;
  switch (frequency.toLowerCase()) {
    case "weekly":
      periodCredit = (monthlyCredit * 12) / 52;
      break;
    case "bi-weekly":
      periodCredit = (monthlyCredit * 12) / 26;
      break;
    case "monthly":
      periodCredit = monthlyCredit;
      break;
    default:
      periodCredit = monthlyCredit;
  }


  return periodCredit;
}

function calculatePAYE(annualSalary, proRataPercentage = 100, frequency = "monthly") {

  // Input validation with detailed logging
  if (!annualSalary || annualSalary <= 0) {
    return 0;
  }

  if (isNaN(proRataPercentage)) {
    proRataPercentage = 100;
  }

  // Calculate annual tax before rebate
  let annualTaxBeforeRebate = 0;
  let applicableBracket = TAX_BRACKETS_2024[0];

  // Find applicable tax bracket
  for (let i = TAX_BRACKETS_2024.length - 1; i >= 0; i--) {
    if (annualSalary > TAX_BRACKETS_2024[i].threshold) {
      applicableBracket = TAX_BRACKETS_2024[i];
      break;
    }
  }

  // Calculate tax using base amount plus percentage of excess
  const excessAmount = annualSalary - applicableBracket.threshold;
  annualTaxBeforeRebate = applicableBracket.base + (excessAmount * applicableBracket.rate);

  // Apply primary rebate
  const primaryRebate = 17235; // R17,235 for 2024/2025
  const annualTaxAfterRebate = Math.max(0, annualTaxBeforeRebate - primaryRebate);


  // Convert annual tax to period tax based on frequency
  let periodTax = 0;
  switch (frequency.toLowerCase()) {
    case "monthly":
      periodTax = annualTaxAfterRebate / 12;
      break;
    case "weekly":
      periodTax = annualTaxAfterRebate / 52;
      break;
    case "biweekly":
      periodTax = annualTaxAfterRebate / 26;
      break;
    default:
      periodTax = annualTaxAfterRebate / 12; // Default to monthly
  }

  // Apply pro-rata percentage after converting to period amount
  const proratedTax = (periodTax * proRataPercentage) / 100;

  // Keep full precision for calculations - only round for display
  const finalTax = proratedTax; // Maintain full precision


  return {
    annualTaxBeforeRebate: annualTaxBeforeRebate,
    primaryRebate: primaryRebate,
    annualTaxAfterRebate: annualTaxAfterRebate,
    applicableBracket: applicableBracket,
    frequency: frequency,
    periodTax: periodTax, // Keep full precision
    proRataPercentage: `${proRataPercentage}%`,
    proratedTax: proratedTax, // Keep full precision
    finalTax: finalTax, // Keep full precision
    // Add display versions for UI
    displayValues: {
      periodTax: periodTax.toFixed(2),
      proratedTax: proratedTax.toFixed(2),
      finalTax: finalTax.toFixed(2)
    }
  };
}

function calculateTotalIncome(
  payroll,
  employee,
  payrollDate,
  isProrated,
  proratedSalary
) {
  let totalIncome = 0;
  let totalTaxableIncome = 0;
  let totalNonTaxableIncome = 0;

  // Basic Salary
  const basicSalary = isProrated ? proratedSalary : payroll.basicSalary || 0;
  totalIncome += basicSalary;
  totalTaxableIncome += basicSalary;

  // Loss of Income (if enabled)
  if (payroll.lossOfIncomeEnabled && payroll.lossOfIncome) {
    const lossOfIncomeCalc = calculateLossOfIncome(payroll.lossOfIncome, payroll.frequency);
    totalIncome += lossOfIncomeCalc.amount;
    totalTaxableIncome += lossOfIncomeCalc.taxableAmount;
    totalNonTaxableIncome += lossOfIncomeCalc.nonTaxableAmount;
  }

  // Add travel allowance - now including total amount in totalIncome
  if (payroll.travelAllowance?.fixedAllowanceAmount) {
    const travelAllowanceDetails = calculateTravelAllowance(payroll.travelAllowance, payroll.basicSalary, employee);
    totalIncome += travelAllowanceDetails.totalAllowance; // Add total allowance to totalIncome
    totalIncome += travelAllowance.fixedAmount;
    totalTaxableIncome += travelAllowanceDetails.taxableAmount; // Add taxable portion to taxableIncome
    totalNonTaxableIncome += travelAllowanceDetails.nonTaxableAmount; // Add non-taxable portion to nonTaxableIncome
    
  }
  
  // Add accommodation benefit
  if (payroll.accommodationBenefit) {
    const accommodationAmount = parseFloat(payroll.accommodationBenefit) || 0;
    totalIncome += accommodationAmount;
    totalTaxableIncome += accommodationAmount; // Accommodation benefit is 100% taxable
    
  }

  // Add commission
  totalIncome += payroll.commission || 0;
  totalTaxableIncome += payroll.commission || 0;

  // Add custom income
  totalIncome += payroll.customIncome || 0;
  totalTaxableIncome += payroll.customIncome || 0;

  // Calculate total deductions
  const totalDeductions = calculateTotalDeductions(
    payroll,
    totalTaxableIncome,
    employee?.payFrequency?.frequency || "monthly",
    payrollDate,
    payrollDate // Using same date since we're calculating for a single period
  );

  // Calculate nett pay (gross income - total deductions)
  const nettPay = totalIncome - totalDeductions;


  return {
    totalIncome: parseFloat(totalIncome.toFixed(2)),
    totalTaxableIncome: parseFloat(totalTaxableIncome.toFixed(2)),
    totalNonTaxableIncome: parseFloat(totalNonTaxableIncome.toFixed(2)),
    totalDeductions: parseFloat(totalDeductions.toFixed(2)),
    nettPay: parseFloat(nettPay.toFixed(2))
  };
}

function convertMonthlyToFrequency(amount, frequency) {
  switch (frequency.toLowerCase()) {
    case "weekly":
      return (amount * 12) / 52;
    case "bi-weekly":
      return (amount * 12) / 26;
    case "monthly":
    default:
      return amount;
  }
}

function generatePayPeriods(
  payFrequency,
  employeeStartDate,
  firstPayrollPeriodEndDate,
  currentDate
) {

  const periods = [];
  let startDate = moment.tz(employeeStartDate, DEFAULT_TIMEZONE).startOf("day");

  // CRITICAL FIX: Don't use company's firstPayrollPeriodEndDate for individual employee periods
  // Calculate proper period end based on employee DOA and pay frequency
  let periodEndDate;

  if (payFrequency.frequency === "monthly") {
    if (payFrequency.lastDayOfPeriod === "monthend") {
      periodEndDate = startDate.clone().endOf("month").endOf("day");
    } else {
      const targetDay = parseInt(payFrequency.lastDayOfPeriod);
      if (startDate.date() <= targetDay) {
        periodEndDate = startDate.clone().date(targetDay).endOf("day");
      } else {
        periodEndDate = startDate.clone().add(1, "month").date(targetDay).endOf("day");
      }
    }
  } else {
    // For non-monthly frequencies, use the provided firstPayrollPeriodEndDate
    periodEndDate = moment.tz(firstPayrollPeriodEndDate, DEFAULT_TIMEZONE).endOf("day");
  }

  const today = moment.tz(currentDate, DEFAULT_TIMEZONE).startOf("day");


  // Handle initial period
  if (startDate.isSameOrBefore(periodEndDate)) {
    periods.push({
      startDate: startDate.toDate(),
      endDate: periodEndDate.toDate(),
      isFinalized: false, // Set to false by default
      isPartial: !startDate.isSame(periodEndDate.clone().startOf("day"), "day"),
    });
  }

  // Generate subsequent periods
  while (periodEndDate.isBefore(today)) {
    const nextStartDate = periodEndDate.clone().add(1, "day").startOf("day");
    periodEndDate = getNextPeriodEndDate(nextStartDate, payFrequency);

    periods.push({
      startDate: nextStartDate.toDate(),
      endDate: periodEndDate.toDate(),
      isFinalized: false, // Set to false by default
      isPartial: false,
    });
  }

  // Add one future period if necessary
  if (periods.length === 0 || periods[periods.length - 1].endDate <= today) {
    const nextStartDate = periodEndDate.clone().add(1, "day").startOf("day");
    periodEndDate = getNextPeriodEndDate(nextStartDate, payFrequency);

    periods.push({
      startDate: nextStartDate.toDate(),
      endDate: periodEndDate.toDate(),
      isFinalized: false,
      isPartial: false,
    });
  }

  return periods;
}

function generateNextPayPeriod(lastPeriodEndDate, payFrequency) {
  const nextStartDate = moment
    .tz(lastPeriodEndDate, DEFAULT_TIMEZONE)
    .add(1, "day")
    .startOf("day");
  const nextEndDate = getNextPeriodEndDate(nextStartDate, payFrequency);

  return {
    startDate: nextStartDate.toDate(),
    endDate: nextEndDate.toDate(),
    isFinalized: false,
    isPartial: false,
  };
}

function getNextPeriodEndDate(startDate, payFrequency) {
  const frequency = payFrequency.frequency.toLowerCase();
  const lastDayOfPeriod = payFrequency.lastDayOfPeriod.toLowerCase();

  let endDate = startDate.clone();

  switch (frequency) {
    case "weekly":
      endDate = endDate.day(lastDayOfPeriod);
      if (endDate.isSameOrBefore(startDate)) {
        endDate.add(1, "week");
      }
      break;
    case "bi-weekly":
    case "bi weekly":
      endDate = endDate.day(lastDayOfPeriod);
      if (endDate.isSameOrBefore(startDate)) {
        endDate.add(2, "weeks");
      }
      break;
    case "monthly":
      if (lastDayOfPeriod === "monthend") {
        endDate = endDate.endOf("month");
      } else {
        endDate = endDate.date(parseInt(lastDayOfPeriod));
        if (endDate.isSameOrBefore(startDate)) {
          endDate.add(1, "month");
        }
      }
      break;
  }

  return endDate.endOf("day");
}

function getNextWeekday(date, dayOfWeek) {
  const daysUntilNextDay = (dayOfWeek - date.getDay() + 7) % 7;
  return date.clone().add(daysUntilNextDay, "days");
}

function formatPayPeriodEndDate(payFrequency, payrollDate) {
  const date = moment.tz(payrollDate, DEFAULT_TIMEZONE);

  switch (payFrequency.frequency.toLowerCase()) {
    case "weekly":
      const weekEnd = date.clone().day(payFrequency.lastDayOfPeriod);
      if (weekEnd.isBefore(date)) {
        weekEnd.add(1, "week");
      }
      return `Week Ending ${weekEnd.format("DD/MM/YYYY")}`;

    case "bi-weekly":
    case "bi weekly":
      const biWeekEnd = date.clone().day(payFrequency.lastDayOfPeriod);
      if (biWeekEnd.isBefore(date)) {
        biWeekEnd.add(2, "weeks");
      } else {
        biWeekEnd.add(1, "week");
      }
      return `Bi-Weekly Ending ${biWeekEnd.format("DD/MM/YYYY")}`;

    case "monthly":
      if (payFrequency.lastDayOfPeriod === "monthend") {
        return `Month Ending ${date.endOf("month").format("DD/MM/YYYY")}`;
      } else {
        const monthEnd = date.clone().date(payFrequency.lastDayOfPeriod);
        if (monthEnd.isBefore(date)) {
          monthEnd.add(1, "month");
        }
        return `Month Ending ${monthEnd.format("DD/MM/YYYY")}`;
      }

    default:
      return date.format("DD/MM/YYYY");
  }
}

function generatePayPeriodsForMonth(
  year,
  month,
  payFrequency,
  lastDayOfPeriod,
  employeeDOA
) {
  const startOfMonth = moment
    .tz([year, month - 1, 1], DEFAULT_TIMEZONE)
    .startOf("day");
  const endOfMonth = startOfMonth.clone().endOf("month");
  const periods = [];
  let currentStart = startOfMonth.clone();

  // Ensure we start from the employee's DOA if it's later than the start of the month
  const employeeStartDate = moment
    .tz(employeeDOA, DEFAULT_TIMEZONE)
    .startOf("day");
  if (employeeStartDate.isAfter(currentStart)) {
    currentStart = employeeStartDate;
  }

  while (currentStart.isSameOrBefore(endOfMonth)) {
    let periodEnd;
    if (payFrequency.toLowerCase() === "weekly") {
      // Find the next occurrence of the last day of the period
      periodEnd = currentStart.clone().day(lastDayOfPeriod);
      if (periodEnd.isSameOrBefore(currentStart)) {
        periodEnd.add(1, "week");
      }
    } else {
      // Handle other frequencies (bi-weekly, monthly) here
    }

    // Ensure the period end doesn't exceed the end of the month
    if (periodEnd.isAfter(endOfMonth)) {
      periodEnd = endOfMonth.clone();
    }

    periods.push({
      startDate: currentStart.toDate(),
      endDate: periodEnd.toDate(),
    });

    currentStart = periodEnd.clone().add(1, "day").startOf("day");
  }

  return periods;
}

function calculateUIF(income, employee, startDate, endDate) {
  const uifRate = 0.01;
  const maxMonthlyUIF = 177.12;

  // Add null check for employee
  if (!employee) {
    return Math.min(income * uifRate, maxMonthlyUIF);
  }

  const periodStart = moment.tz(startDate, DEFAULT_TIMEZONE);
  const periodEnd = moment.tz(endDate, DEFAULT_TIMEZONE);
  const isFirstWeekOfMonth = periodStart.date() <= 7;
  const isSecondWeekOfMonth =
    periodStart.date() > 7 && periodStart.date() <= 14;


  // For weekly frequency, calculate UIF directly as 1% of the period income
  if (employee.payFrequency && employee.payFrequency.frequency === 'weekly') {
    return Number((income * uifRate).toFixed(2));
  }

  // For other frequencies, use monthly calculations
  const fullMonthlyIncome = convertToMonthlyAmount(income, employee.payFrequency?.frequency || 'monthly');
  const fullMonthlyUIF = Math.min(fullMonthlyIncome * uifRate, maxMonthlyUIF);
  const currentPeriodUIF = Math.min(income * uifRate, maxMonthlyUIF);

  if (isFirstWeekOfMonth) {
    if (currentPeriodUIF < fullMonthlyUIF) {
      const remainingUIF = fullMonthlyUIF - currentPeriodUIF;
      storeRemainingUIF(employee._id, periodStart, remainingUIF);
    }
    return currentPeriodUIF;
  }

  if (isSecondWeekOfMonth) {
    const remainingUIF = getRemainingUIF(employee._id, periodStart);
    if (remainingUIF > 0) {
      return remainingUIF;
    }
  }

  return 0;
}

// Add this function to calculate period-based UIF
function calculatePeriodUIF(periodSalary = 0, frequency = 'monthly', payroll = null, options = {}) {
  
  // Ensure payroll object exists
  payroll = payroll || {};
  

  // UIF is calculated at 1% of total remuneration
  const UIF_RATE = 0.01;
  const MAX_MONTHLY_UIF = 177.12;
  const MAX_MONTHLY_SALARY = 17712.00;

  // Default to monthly if frequency is not provided
  frequency = (frequency || 'monthly').toLowerCase();

  // For weekly frequency, simply calculate 1% of the period salary
  if (frequency === "weekly") {
    // Get the total income for the period
    const totalIncome = periodSalary || 0;
    const uifAmount = totalIncome * UIF_RATE;
    

    return Math.min(uifAmount, MAX_MONTHLY_UIF / 4);
  }

  // For monthly frequency
  // Use periodSalary if available, otherwise use basic salary or 0
  const totalIncome = periodSalary || payroll?.basicSalary || 0;
  
  // Calculate UIF amount (1% of income)
  const uifAmount = Math.min(totalIncome, MAX_MONTHLY_SALARY) * UIF_RATE;
  

  return Math.min(uifAmount, MAX_MONTHLY_UIF);
}

function processWeeklyPayroll(
  employee,
  payPeriod,
  yearToDateEarnings = 0,
  yearToDateTaxPaid = 0
) {
  const { startDate, endDate } = payPeriod;
  const weeklyBasicSalary = employee.basicSalary;

  const proratedResult = calculateProratedSalary(
    employee.doa,
    weeklyBasicSalary,
    employee,
    startDate,
    endDate
  );
  const periodEarnings = parseFloat(proratedResult.proratedSalary);

  const annualSalary = weeklyBasicSalary * 52;
  const paye = calculatePAYE(
    annualSalary,
    parseFloat(proratedResult.proratedPercentage)
  );
  const uif = calculateUIF(periodEarnings, employee, startDate, endDate);

  const netPay = periodEarnings - paye - uif;

  return {
    payPeriod,
    grossEarnings: periodEarnings,
    paye,
    uif,
    netPay,
    yearToDateEarnings: yearToDateEarnings + periodEarnings,
    yearToDateTaxPaid: yearToDateTaxPaid + paye,
    proratedPercentage: proratedResult.proratedPercentage,
    workedDays: proratedResult.workedDays,
    totalDaysInPeriod: proratedResult.totalDaysInPeriod,
  };
}

// Add this new function to handle period-based salary calculations
function calculatePeriodBasedSalary(
  basicSalary,
  payFrequency,
  startDate,
  endDate,
  isFirstPeriod
) {

  // Convert monthly salary to period salary
  let periodSalary;
  switch (payFrequency.frequency.toLowerCase()) {
    case "weekly":
      periodSalary = (basicSalary * 12) / 52;
      break;
    case "bi-weekly":
      periodSalary = (basicSalary * 12) / 26;
      break;
    case "monthly":
      periodSalary = basicSalary;
      break;
    default:
      periodSalary = basicSalary;
  }

  // If it's first period or mid-period start, calculate prorated amount
  if (isFirstPeriod) {
    const proratedResult = calculateProratedSalary(
      startDate,
      periodSalary,
      { payFrequency },
      startDate,
      endDate
    );
    periodSalary = parseFloat(proratedResult.proratedSalary);
  }


  return periodSalary;
}

// Update PAYE calculation to handle different frequencies
function calculatePeriodPAYE(
  annualSalary,
  payFrequency,
  startDate,
  endDate,
  isFirstPeriod
) {
  const periodTax = calculatePAYE(
    annualSalary,
    isFirstPeriod
      ? calculateProratedPercentage(
          moment.tz(startDate, DEFAULT_TIMEZONE),
          moment.tz(endDate, DEFAULT_TIMEZONE)
        )
      : 100,
    payFrequency.frequency
  );

  return periodTax;
}

function calculateMedicalAidTaxableBenefit(
  employerContribution = 0,
  medicalAid = 0, // Renamed from employeeContribution to medicalAid
  employeeHandlesPayment = false
) {

  let taxableBenefit = 0;
  const employeeContribution = medicalAid || 0; // Use medicalAid amount

  // If employer pays directly to the medical aid
  if (!employeeHandlesPayment) {
    taxableBenefit = employerContribution;
  }


  return taxableBenefit;
}

function calculateMedicalAidTaxImpact(
  medicalAidDetails,
  frequency = "monthly",
  salary = 0
) {
  const {
    members = 0,
    employerContribution = 0,
    medicalAid = 0, // Use medicalAid instead of employeeContribution
    employeeHandlesPayment = false,
    dontApplyTaxCredits = false,
  } = medicalAidDetails;

  // Calculate tax credit
  const taxCredit = dontApplyTaxCredits
    ? 0
    : calculateMedicalAidCredit(members, frequency);

  // Calculate taxable benefit using medicalAid amount
  const taxableBenefit = calculateMedicalAidTaxableBenefit(
    employerContribution,
    medicalAid, // Pass medicalAid here
    employeeHandlesPayment
  );

  // Calculate net deduction using medicalAid amount
  const netDeduction = employeeHandlesPayment
    ? medicalAid
    : medicalAid - employerContribution;


  return {
    taxCredit,
    taxableBenefit,
    netDeduction,
    totalTaxSaving: taxCredit * (salary > 0 ? 1 : 0),
  };
}

function calculateTotalTaxableIncome(payroll = {}, frequency = "monthly") {
  let totalTaxableIncome = 0;

  // Add basic salary
  totalTaxableIncome += payroll.basicSalary || 0;
  
  // Add accommodation benefit (100% taxable)
  if (payroll?.accommodationBenefit) {
    const accommodationAmount = parseFloat(payroll.accommodationBenefit) || 0;
    totalTaxableIncome += accommodationAmount;
    
  }

  // Add travel allowance (80% taxable)
  if (payroll.travelAllowance) {
    const travelAllowanceDetails = calculateTravelAllowance(payroll.travelAllowance, payroll.basicSalary, payroll.employee);
    totalTaxableIncome += travelAllowanceDetails.taxableAmount || 0;
  }

  // Add loss of income (if taxable)
  if (payroll.lossOfIncome) {
    totalTaxableIncome += payroll.lossOfIncome;
  }

  // Add commission
  if (payroll.commission) {
    totalTaxableIncome += payroll.commission;
  }

  // Add medical aid taxable benefit
  if (payroll.medicalAid?.employerContribution) {
    const medicalAidTaxableBenefit = calculateMedicalAidTaxableBenefit(
      payroll.medicalAid.employerContribution,
      payroll.medicalAid.employeeContribution,
      payroll.medicalAid.employeeHandlesPayment
    );
    totalTaxableIncome += medicalAidTaxableBenefit;
  }


  return totalTaxableIncome;
}

function calculateTotalDeductions(
  payrollData,
  taxableIncome,
  frequency,
  periodStartDate,
  periodEndDate
) {
  let totalDeductions = 0;

  // Calculate PAYE
  const annualTaxableIncome = convertToAnnualAmount(taxableIncome, frequency);
  const monthlyAccommodationBenefit = payrollData?.accommodationBenefit ? parseFloat(payrollData.accommodationBenefit) : 0;
  
  
  try {
    const paye = calculateEnhancedPAYE({ 
      annualSalary: annualTaxableIncome, // Don't subtract accommodation benefit here
      frequency,
      age: payrollData?.employee?.age || 0,
      accommodationBenefit: monthlyAccommodationBenefit,
      travelAllowance: payrollData?.travelAllowance?.fixedAllowanceAmount || 0
    });
    
    // Make sure monthlyPAYE is a valid number
    const monthlyPAYE = paye && typeof paye.monthlyPAYE === 'number' ? paye.monthlyPAYE : 0;
    totalDeductions += monthlyPAYE;
    
    // Calculate UIF
    const uif = calculatePeriodUIF(taxableIncome, frequency, payrollData);
    totalDeductions += uif;
  
    // Add medical aid employee contribution
    if (payrollData.medicalAid?.employeeContribution) {
      totalDeductions += payrollData.medicalAid.employeeContribution;
    }
  
    // Add pension fund contribution
    if (payrollData.pensionFund?.employeeContribution) {
      totalDeductions += payrollData.pensionFund.employeeContribution;
    }
  
    // Add retirement annuity fund
    if (payrollData.retirementAnnuityFund?.employeeContribution) {
      totalDeductions += payrollData.retirementAnnuityFund.employeeContribution;
    }
  
    // Add any other deductions from payrollData
    if (payrollData.otherDeductions) {
      totalDeductions += parseFloat(payrollData.otherDeductions) || 0;
    }
  
  } catch (error) {
    console.error("Error calculating deductions:", error);
    // Return a safe value in case of error
    return 0;
  }

  return Number(totalDeductions.toFixed(2));
}

// Helper function to convert amount to annual based on frequency
function convertToAnnualAmount(amount, frequency) {
  switch (frequency.toLowerCase()) {
    case "weekly":
      return amount * 52;
    case "bi-weekly":
      return amount * 26;
    case "monthly":
      return amount * 12;
    default:
      return amount;
  }
}

function calculateAnnualBonusTax(
  bonusAmount,
  annualSalary,
  frequency = "monthly"
) {

  // Convert annual salary to yearly if it's not already
  const yearlyBaseSalary = convertToAnnualAmount(annualSalary, frequency);

  // Calculate tax on salary without bonus
  const baseTax = calculateAnnualTax(yearlyBaseSalary);

  // Calculate tax on salary plus bonus
  const totalTax = calculateAnnualTax(yearlyBaseSalary + bonusAmount);

  // The difference is the tax on the bonus
  const bonusTax = totalTax - baseTax;


  return {
    bonusTax,
    effectiveRate: ((bonusTax / bonusAmount) * 100).toFixed(2),
    grossBonus: bonusAmount,
    netBonus: bonusAmount - bonusTax,
  };
}

function calculateAnnualTax(annualAmount) {
  // Using 2024 tax brackets
  let tax = 0;

  for (const bracket of TAX_BRACKETS_2024) {
    if (annualAmount > bracket.threshold) {
      const taxableInThisBracket = Math.min(
        annualAmount - bracket.threshold, 
        bracket.nextBracketThreshold 
          ? bracket.nextBracketThreshold - bracket.threshold 
          : annualAmount
      );
      
      const taxOnBracket = bracket.base + (taxableInThisBracket * bracket.rate);
      tax = bracket.base + (taxableInThisBracket * bracket.rate);
    }
  }

  // Subtract primary rebate
  const primaryRebate = 17235; // R17,235 for 2024 tax year
  return Math.max(0, tax - primaryRebate);
}

// Add this function to calculate period-based UIF
function calculatePeriodEndDate(startDate, frequency = "monthly") {
  const start = moment.tz(startDate, DEFAULT_TIMEZONE);

  switch (frequency.toLowerCase()) {
    case "weekly":
      return start.clone().endOf("week");

    case "bi-weekly":
    case "bi weekly":
      return start.clone().add(13, "days").endOf("day");

    case "monthly":
      return start.clone().endOf("month");

    default:
      return start.clone().endOf("month");
  }
}

function calculateTravelAllowance(travelAllowance, monthlyBasicSalary, employee) {
  if (!travelAllowance) return { totalAllowance: 0, taxableAmount: 0, nonTaxableAmount: 0 };

  let totalAllowance = 0;
  let taxableAmount = 0;
  let nonTaxableAmount = 0;

  // Fixed Allowance Calculation
  if (travelAllowance.fixedAllowance && travelAllowance.fixedAllowanceAmount) {
    const fixedAmount = parseFloat(travelAllowance.fixedAllowanceAmount);
    totalAllowance += fixedAmount;
    
    // Determine taxable percentage based on only20PercentTax flag
    const taxablePercentage = travelAllowance.only20PercentTax ? 0.20 : 0.80;
    taxableAmount += fixedAmount * taxablePercentage;
    nonTaxableAmount += fixedAmount * (1 - taxablePercentage);
  }

  // Reimbursed Expenses Calculation
  if (travelAllowance.reimbursedExpenses) {
    // Per Kilometer Calculation
    if (travelAllowance.reimbursedPerKmTravelled && travelAllowance.ratePerKm) {
      const kmRate = parseFloat(travelAllowance.ratePerKm);
      const kmAllowance = kmRate * (travelAllowance.businessKilometers || 0);
      
      // Compare with SARS prescribed rate
      const maxTaxFreeKm = kmAllowance > 0 
        ? Math.min(kmAllowance, SARS_KILOMETER_RATE * (travelAllowance.businessKilometers || 0))
        : 0;
      
      totalAllowance += kmAllowance;
      nonTaxableAmount += maxTaxFreeKm;
      taxableAmount += Math.max(0, kmAllowance - maxTaxFreeKm);
    }

    // Petrol Card Benefit
    if (travelAllowance.companyPetrolCard) {
      // Treat entire petrol card benefit as taxable
      const petrolCardValue = parseFloat(travelAllowance.petrolCardValue || 0);
      totalAllowance += petrolCardValue;
      taxableAmount += petrolCardValue;
    }
  }

  // Additional Validation
  const MAX_REASONABLE_KM_PER_MONTH = 5000; // Reasonable limit
  if ((travelAllowance.businessKilometers || 0) > MAX_REASONABLE_KM_PER_MONTH) {
  }

  return {
    totalAllowance: parseFloat(totalAllowance.toFixed(2)),
    taxableAmount: parseFloat(taxableAmount.toFixed(2)),
    nonTaxableAmount: parseFloat(nonTaxableAmount.toFixed(2)),
    sarsPrescribedRate: SARS_KILOMETER_RATE,
    irpCodes: {
      fixedAllowance: 3701, // IRP5 code for fixed travel allowance
      reimbursement: 3702,  // IRP5 code for reimbursed travel
      petrolCard: 3703      // IRP5 code for petrol card benefit
    }
  };
}

// Enhanced UIF Calculation
function calculateEnhancedUIF(income, travelAllowanceTaxable) {
  const UIF_RATE = 0.01;
  const UIF_CAP = 177.12;
  
  const totalTaxableIncome = income + (travelAllowanceTaxable || 0);
  const uifAmount = Math.min(totalTaxableIncome * UIF_RATE, UIF_CAP);
  
  return {
    uifAmount: parseFloat(uifAmount.toFixed(2)),
    totalTaxableIncome: parseFloat(totalTaxableIncome.toFixed(2))
  };
}

// Enhanced PAYE Calculation with Detailed Tax Breakdown
function calculateEnhancedPAYE({ annualSalary, age = 0, frequency = "monthly", accommodationBenefit = 0, travelAllowance = 0 }) {
  // Convert accommodation benefit to annual amount
  const annualAccommodationBenefit = accommodationBenefit * 12;
  
  // Calculate travel allowance taxable amount (80% of travel allowance is taxable)
  const annualTravelAllowance = travelAllowance * 12;
  const travelAllowanceTaxable = annualTravelAllowance * 0.8;

  // Step 0: Calculate total annual income (including accommodation benefit and travel allowance)
  const totalAnnualIncome = annualSalary + annualAccommodationBenefit + travelAllowanceTaxable;

  // Find the applicable tax bracket - with safeguards for undefined result
  let applicableBracket = TAX_BRACKETS_2024.find(bracket => 
    totalAnnualIncome > bracket.threshold && 
    (!bracket.nextBracketThreshold || totalAnnualIncome <= bracket.nextBracketThreshold)
  );
  
  // If no applicable bracket is found (shouldn't happen but let's be safe),
  // use the first bracket as default
  if (!applicableBracket) {
    applicableBracket = TAX_BRACKETS_2024[0] || { 
      threshold: 0, 
      rate: 0.18, 
      base: 0, 
      nextBracketThreshold: 237100 
    };
  }

  // 2023/2024 Tax Year Rebates
  const PRIMARY_REBATE = 17235;
  const SENIOR_REBATE_65_TO_75 = 9444;
  const SENIOR_REBATE_75_PLUS = 3145;

  // Determine applicable rebate based on age
  let totalRebate = PRIMARY_REBATE;
  let seniorRebate = 0;
  
  if (age >= 65 && age < 75) {
    seniorRebate = SENIOR_REBATE_65_TO_75;
    totalRebate += seniorRebate;
  } else if (age >= 75) {
    seniorRebate = SENIOR_REBATE_65_TO_75 + SENIOR_REBATE_75_PLUS;
    totalRebate += seniorRebate;
  }

  // Step 1: Calculate excess amount based on total annual income
  const excessAmount = Math.max(0, totalAnnualIncome - applicableBracket.threshold);
  

  // Step 2: Multiply excess amount by tax rate
  const taxOnExcess = excessAmount * applicableBracket.rate;

  // Step 3: Add base tax amount
  const totalAnnualTax = applicableBracket.base + taxOnExcess;

  // Step 4: Subtract rebate
  const annualTaxAfterRebate = Math.max(0, totalAnnualTax - totalRebate);

  // Step 5: Convert to period-based PAYE based on frequency
  let periodPAYE;
  switch (frequency.toLowerCase()) {
    case "weekly":
      periodPAYE = annualTaxAfterRebate / 52;
      break;
    case "biweekly":
      periodPAYE = annualTaxAfterRebate / 26;
      break;
    case "monthly":
    default:
      periodPAYE = annualTaxAfterRebate / 12;
      break;
  }

  // Keep monthlyPAYE for backward compatibility
  const monthlyPAYE = annualTaxAfterRebate / 12;

  return {
    annualSalary: totalAnnualIncome,
    totalRebate,
    annualPAYE: annualTaxAfterRebate,
    monthlyPAYE: monthlyPAYE, // For backward compatibility
    periodPAYE: periodPAYE, // New: frequency-specific PAYE
    weeklyPAYE: frequency.toLowerCase() === 'weekly' ? periodPAYE : annualTaxAfterRebate / 52, // Explicit weekly PAYE
    frequency: frequency,
    taxBracket: `${applicableBracket.threshold} - ${applicableBracket.nextBracketThreshold || 'Above'}`,
    employeeAge: age,
    rebateDetails: {
      primaryRebate: PRIMARY_REBATE,
      seniorRebate: seniorRebate
    }
  };
}

// Helper functions to store and retrieve remaining UIF
// You'll need to implement these with your database
async function storeRemainingUIF(employeeId, period, amount) {
  try {
    await PayrollPeriod.findOneAndUpdate(
      {
        employee: employeeId,
        startDate: {
          $gte: moment(period).add(1, "week").startOf("day").toDate(),
        },
      },
      {
        $set: {
          remainingUIF: amount,
        },
      },
      { new: true }
    );
  } catch (error) {
    console.error("Error storing remaining UIF:", error);
  }
}

async function getRemainingUIF(employeeId, period) {
  try {
    const payrollPeriod = await PayrollPeriod.findOne({
      employee: employeeId,
      startDate: period.toDate(),
    });
    return payrollPeriod?.remainingUIF || 0;
  } catch (error) {
    console.error("Error retrieving remaining UIF:", error);
    return 0;
  }
}

// Helper function to convert amount to monthly based on frequency
function convertToMonthlyAmount(amount, frequency) {
  switch (frequency.toLowerCase()) {
    case "weekly":
      return amount * 52 / 12;
    case "bi-weekly":
      return amount * 26 / 12;
    case "monthly":
      return amount;
    default:
      return amount;
  }
}

// Calculate Loss of Income benefit
function calculateLossOfIncome(lossOfIncomeAmount, frequency = "monthly") {
  if (!lossOfIncomeAmount || lossOfIncomeAmount < 0) {
    return {
      amount: 0,
      taxableAmount: 0,
      nonTaxableAmount: 0
    };
  }

  // Loss of Income is non-taxable and reported under code 3602
  return {
    amount: lossOfIncomeAmount,
    taxableAmount: 0,
    nonTaxableAmount: lossOfIncomeAmount
  };
}

// Calculate period start date based on pay frequency settings
function calculatePeriodStartDate(date, payFrequency) {
  const currentDate = moment.tz(date, DEFAULT_TIMEZONE);
  
  switch (payFrequency.frequency.toLowerCase()) {
    case "weekly":
      return currentDate.clone().startOf("week");
      
    case "biweekly":
    case "bi-weekly":
    case "bi weekly":
      // For biweekly, start date depends on the firstPayrollPeriodEndDate
      const firstEndDate = moment.tz(payFrequency.firstPayrollPeriodEndDate, DEFAULT_TIMEZONE);
      const dayDiff = currentDate.diff(firstEndDate, 'days');
      const periodNum = Math.floor(dayDiff / 14);
      return firstEndDate.clone().add(periodNum * 14 + 1, 'days').startOf('day');
      
    case "monthly":
      if (payFrequency.lastDayOfPeriod === "monthend") {
        // For monthend frequency, start from first of month
        return currentDate.clone().startOf("month");
      } else {
        // For custom end dates (e.g., 25th of each month)
        const customEndDay = parseInt(payFrequency.lastDayOfPeriod);
        const currentMonth = currentDate.clone().startOf('month');
        
        // Find the period end date for the current month
        const currentPeriodEnd = currentMonth.clone().date(customEndDay);
        if (currentDate.isAfter(currentPeriodEnd)) {
          // We're after the current period end, so start date is the day after
          return currentPeriodEnd.clone().add(1, 'day').startOf('day');
        } else {
          // We're before or on the current period end, so start date is the day after previous period end
          return currentMonth.clone().subtract(1, 'month').date(customEndDay).add(1, 'day').startOf('day');
        }
      }
      
    default:
      return currentDate.clone().startOf("month");
  }
}

module.exports = {
  isProratedMonth,
  calculateAge,
  calculateProratedSalary,
  calculateProratedPercentage,
  calculatePeriodStartDate,
  calculateSalaryForPayPeriod,
  calculateCompanyCarBenefit,
  calculateCompanyCarUnderOperatingLeaseBenefit,
  calculateIncomeProtectionBenefit,
  calculateRetirementAnnuityFundBenefit,
  calculateMedicalAidCredit,
  calculatePAYE,
  calculateTotalIncome,
  convertMonthlyToFrequency,
  generatePayPeriods,
  generateNextPayPeriod,
  formatPayPeriodEndDate,
  generatePayPeriodsForMonth,
  calculateUIF,
  processWeeklyPayroll,
  calculatePeriodBasedSalary,
  calculatePeriodPAYE,
  calculatePeriodUIF,
  convertToMonthlyAmount,
  calculateMedicalAidTaxableBenefit,
  calculateMedicalAidTaxImpact,
  calculateTotalTaxableIncome,
  calculateTotalDeductions,
  calculateAnnualBonusTax,
  calculateAnnualTax,
  getNextPeriodEndDate,
  calculatePeriodEndDate,
  calculateTravelAllowance,
  SARS_KILOMETER_RATE,
  calculateEnhancedUIF,
  calculateEnhancedPAYE,
  calculateAge
};
