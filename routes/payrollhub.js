const express = require("express");
const router = express.Router({ mergeParams: true });
const mongoose = require("mongoose");
const { ensureAuthenticated } = require("../config/ensureAuthenticated");
const csrfProtection = require("../middleware/csrfProtection");
const moment = require("moment-timezone");
const PayrollPeriod = require("../models/PayrollPeriod");
const Employee = require("../models/Employee");
const Company = require("../models/Company");
const PayRun = require("../models/payRun");
const EFTDetails = require("../models/eftDetails");
const Payroll = require("../models/Payroll");
const PayrollService = require("../services/PayrollService");
const AuditLog = require("../models/auditLog");
const payrollCalculations = require("../utils/payrollCalculations");
const Integration = require("../models/Integration");
const XeroAccountMapping = require("../models/xeroAccountMapping");
const { generateFNBBankFile } = require("../utils/bankFileGenerator");

// Helper function to format currency
const formatCurrency = (amount) => {
  return new Intl.NumberFormat("en-ZA", {
    style: "currency",
    currency: "ZAR",
    minimumFractionDigits: 2,
  }).format(amount || 0);
};

// Helper function to format pay period
const formatPayPeriod = (startDate, endDate) => {
  if (!startDate || !endDate) return "N/A";
  return `${moment(startDate).format("DD MMM")} - ${moment(endDate).format(
    "DD MMM YYYY"
  )}`;
};

// Helper function to calculate deductions
const calculateDeductions = (payslip) => {
  let totalDeductions = 0;
  const uif = Math.min((payslip.basicSalary || 0) * 0.01, 177.12);
  totalDeductions += uif;

  const annualSalary = (payslip.basicSalary || 0) * 12;
  let paye = 0;

  const TAX_BRACKETS = [
    { threshold: 0, rate: 0.18, base: 0 },
    { threshold: 237100, rate: 0.26, base: 42678 },
    { threshold: 370500, rate: 0.31, base: 77362 },
    { threshold: 512800, rate: 0.36, base: 121475 },
    { threshold: 673000, rate: 0.39, base: 179147 },
    { threshold: 857900, rate: 0.41, base: 251258 },
    { threshold: 1817000, rate: 0.45, base: 644489 },
  ];

  let bracket = TAX_BRACKETS[0];
  for (let i = 1; i < TAX_BRACKETS.length; i++) {
    if (annualSalary >= TAX_BRACKETS[i].threshold) {
      bracket = TAX_BRACKETS[i];
    } else {
      break;
    }
  }

  const annualTax =
    bracket.base + bracket.rate * (annualSalary - bracket.threshold);
  const primaryRebate = 17235;
  const annualTaxAfterRebate = Math.max(0, annualTax - primaryRebate);
  paye = annualTaxAfterRebate / 12;
  totalDeductions += paye;

  if (payslip.pensionFund) totalDeductions += payslip.pensionFund;
  if (payslip.providentFund) totalDeductions += payslip.providentFund;
  if (payslip.retirementAnnuity) totalDeductions += payslip.retirementAnnuity;
  if (payslip.garnishee) totalDeductions += payslip.garnishee;
  if (payslip.maintenanceOrder) totalDeductions += payslip.maintenanceOrder;
  if (payslip.medical && payslip.medical.medicalAid)
    totalDeductions += payslip.medical.medicalAid;

  payslip.calculations = {
    deductions: {
      statutory: { paye, uif },
    },
  };
  return totalDeductions;
};

// Helper function to calculate gross income
const calculateGrossIncome = (payslip) => {
  let totalIncome = payslip.basicSalary || 0;
  if (payslip.commission) totalIncome += payslip.commission;
  if (payslip.lossOfIncome) totalIncome += payslip.lossOfIncome;
  if (payslip.travelAllowance && payslip.travelAllowance.fixedAllowanceAmount) {
    totalIncome += payslip.travelAllowance.fixedAllowanceAmount;
  }
  return totalIncome;
};

// Helper function to get progress status icon
const getProgressStatusIcon = (status) => {
  switch (status.toLowerCase()) {
    case "draft":
    case "pending":
      return "ph-clock";
    case "processing":
      return "ph-spinner";
    case "finalized":
      return "ph-check-circle";
    case "released":
      return "ph-upload";
    default:
      return "ph-circle";
  }
};

// Helper function to calculate period statistics
const calculatePeriodStats = (periods = []) => {
  if (!Array.isArray(periods))
    return {
      totalCount: 0,
      finalizedCount: 0,
      unfinalizedCount: 0,
      totalBasicSalary: 0,
      employeeCount: 0,
    };

  return {
    totalCount: periods.length,
    finalizedCount: periods.filter((p) => p && p.isFinalized).length,
    unfinalizedCount: periods.filter((p) => p && !p.isFinalized).length,
    totalBasicSalary: periods.reduce(
      (sum, p) => sum + (p && p.basicSalary ? p.basicSalary : 0),
      0
    ),
    employeeCount: new Set(
      periods
        .filter((p) => p && p.employee && p.employee._id)
        .map((p) => p.employee._id.toString())
    ).size,
  };
};

// Helper function to group periods by month and frequency
const groupPeriodsByMonthAndFrequency = (periods = []) => {

  if (!Array.isArray(periods)) {
    return {};
  }

  const grouped = {};

  periods.forEach((period) => {
    if (!period || !period.endDate || !period.frequency) {
      return;
    }

    const frequency = period.frequency.toLowerCase();
    const monthYear = moment(period.endDate).format("YYYY-MM");


    if (!grouped[frequency]) {
      grouped[frequency] = {};
    }
    if (!grouped[frequency][monthYear]) {
      grouped[frequency][monthYear] = {
        periods: [],
        stats: null,
      };
    }

    grouped[frequency][monthYear].periods.push(period);
  });

  // Calculate stats for each group
  Object.keys(grouped).forEach((frequency) => {
    Object.keys(grouped[frequency]).forEach((monthYear) => {
      grouped[frequency][monthYear].stats = calculatePeriodStats(
        grouped[frequency][monthYear].periods
      );
    });
  });

  return grouped;
};

// Helper function to calculate total taxable income
function calculateTotalTaxableIncome(payrollData) {

  const basicSalary = Number(payrollData.basicSalary) || 0;
  const commission = Number(payrollData.commission) || 0;
  const travelAllowance = payrollData.travelAllowance?.amount
    ? Number(payrollData.travelAllowance.amount)
    : 0;


  const totalTaxableIncome = basicSalary + commission + travelAllowance;

  return totalTaxableIncome;
}

// Helper function to calculate total deductions
function calculateTotalDeductions(
  payrollData,
  taxableIncome,
  frequency,
  startDate,
  endDate
) {

  try {
    // Initialize deductions object
    const deductions = {
      statutory: {
        paye: 0,
        uif: 0,
      },
      nonStatutory: {
        medicalAid: 0,
        pensionFund: 0,
        retirementAnnuity: 0,
      },
    };

    // Calculate PAYE if applicable
    if (taxableIncome > 0) {
      // Assuming monthly frequency for now
      const annualTaxableIncome =
        frequency === "monthly" ? taxableIncome * 12 : taxableIncome;
      deductions.statutory.paye = calculatePAYE(annualTaxableIncome, frequency);
    }

    // Calculate UIF
    if (!payrollData.employee?.isUifExempt) {
      const uifableIncome = payrollData.basicSalary || 0;
      deductions.statutory.uif = Math.min(uifableIncome * 0.01, 148.72); // 1% of income, capped at R148.72
    }

    // Calculate non-statutory deductions
    if (payrollData.medicalAid?.amount) {
      deductions.nonStatutory.medicalAid = Number(
        payrollData.medicalAid.amount
      );
    }
    if (payrollData.pensionFund?.amount) {
      deductions.nonStatutory.pensionFund = Number(
        payrollData.pensionFund.amount
      );
    }
    if (payrollData.retirementAnnuityFund?.amount) {
      deductions.nonStatutory.retirementAnnuity = Number(
        payrollData.retirementAnnuityFund.amount
      );
    }


    // Calculate total deductions
    const totalDeductions =
      Object.values(deductions.statutory).reduce((a, b) => a + b, 0) +
      Object.values(deductions.nonStatutory).reduce((a, b) => a + b, 0);

    return totalDeductions;
  } catch (error) {
    console.error("Error calculating deductions:", error);
    throw error;
  }
}

// Helper function to calculate PAYE
function calculatePAYE(annualTaxableIncome, frequency) {

  // 2024/2025 tax brackets
  const taxBrackets = [
    { threshold: 0, rate: 0.18, base: 0 },
    { threshold: 237100, rate: 0.26, base: 42678 },
    { threshold: 370500, rate: 0.31, base: 77362 },
    { threshold: 512800, rate: 0.36, base: 121475 },
    { threshold: 673000, rate: 0.39, base: 179147 },
    { threshold: 857900, rate: 0.41, base: 251258 },
    { threshold: 1817000, rate: 0.45, base: 644489 },
  ];

  // Find applicable tax bracket
  let bracket = taxBrackets[0];
  for (let i = 1; i < taxBrackets.length; i++) {
    if (annualTaxableIncome >= taxBrackets[i].threshold) {
      bracket = taxBrackets[i];
    } else {
      break;
    }
  }


  // Calculate annual tax
  const annualTax =
    bracket.base + bracket.rate * (annualTaxableIncome - bracket.threshold);

  // Apply primary rebate
  const primaryRebate = 17235;
  const annualTaxAfterRebate = Math.max(0, annualTax - primaryRebate);

  // Convert to period amount based on frequency
  const periodTax =
    frequency === "monthly" ? annualTaxAfterRebate / 12 : annualTaxAfterRebate;

  return periodTax;
}

// UIF calculation function
const calculateUIF = ({ salary, employee, startDate, endDate }) => {

  try {
    if (!salary || !employee) {
      return 0;
    }

    // Check if employee is UIF exempt
    if (employee.isUifExempt) {
      return 0;
    }

    // UIF is 1% of salary, capped at R148.72 per month
    const UIF_RATE = 0.01;
    const UIF_CAP = 148.72;

    const uifAmount = Math.min(salary * UIF_RATE, UIF_CAP);

    return uifAmount;
  } catch (error) {
    console.error("Error calculating UIF:", error);
    return 0;
  }
};

// PAYE calculation function with improved logging
const calculatePAYEImproved = ({
  annualSalary,
  frequency = "monthly",
  proRataPercentage = 100,
}) => {

  try {
    // Validate inputs
    if (typeof annualSalary !== "number" || annualSalary < 0) {
      return 0;
    }

    if (
      typeof proRataPercentage !== "number" ||
      proRataPercentage < 0 ||
      proRataPercentage > 100
    ) {
      proRataPercentage = 100;
    }

    // Tax brackets for 2024/2025
    const TAX_BRACKETS = [
      { threshold: 0, rate: 0.18, base: 0, nextBracketThreshold: 237100 },
      {
        threshold: 237101,
        rate: 0.26,
        base: 42678,
        nextBracketThreshold: 370500,
      },
      {
        threshold: 370501,
        rate: 0.31,
        base: 77362,
        nextBracketThreshold: 512800,
      },
      {
        threshold: 512801,
        rate: 0.36,
        base: 121475,
        nextBracketThreshold: 673000,
      },
      {
        threshold: 673001,
        rate: 0.39,
        base: 179147,
        nextBracketThreshold: 857900,
      },
      {
        threshold: 857901,
        rate: 0.41,
        base: 251258,
        nextBracketThreshold: 1817000,
      },
      {
        threshold: 1817001,
        rate: 0.45,
        base: 644489,
        nextBracketThreshold: Infinity,
      },
    ];

    // Find applicable tax bracket
    const bracket =
      TAX_BRACKETS.find(
        (b) =>
          annualSalary >= b.threshold && annualSalary < b.nextBracketThreshold
      ) || TAX_BRACKETS[0];


    // Calculate annual tax
    const annualTaxBeforeRebate =
      bracket.base + bracket.rate * (annualSalary - bracket.threshold);

    // Apply primary rebate
    const PRIMARY_REBATE = 17235;
    const annualTaxAfterRebate = Math.max(
      0,
      annualTaxBeforeRebate - PRIMARY_REBATE
    );

    // Convert to period amount based on frequency
    let periodDivisor = 12; // monthly
    if (frequency === "weekly") periodDivisor = 52;
    if (frequency === "biweekly") periodDivisor = 26;

    const periodTax =
      (annualTaxAfterRebate / periodDivisor) * (proRataPercentage / 100);

    return Math.round(periodTax * 100) / 100;
  } catch (error) {
    console.error("Error calculating PAYE:", error);
    return 0;
  }
};

// Add debug logging for payroll data processing
const processPayrollData = async (payslip) => {

  if (!payslip) {
    return null;
  }

  try {
    // Get employee details
    const employee = payslip.employee;
    if (!employee) {
      return null;
    }


    // Calculate payroll
    const calculations = await calculatePayroll({
      employee: employee._id,
      startDate: payslip.startDate,
      endDate: payslip.endDate,
      frequency: payslip.frequency,
    });


    return {
      ...payslip,
      calculations,
    };
  } catch (error) {
    console.error("Error processing payroll data:", error);
    return null;
  }
};

// Process pending payslips
const processPendingPayslips = async (payslips) => {

  if (!payslips || !Array.isArray(payslips)) {
    return [];
  }

  const processedPayslips = [];
  for (const payslip of payslips) {
    try {
      const processedPayslip = await processPayrollData(payslip);
      if (processedPayslip) {
        const employee = processedPayslip.employee;
        const employeeName = employee
          ? `${employee.firstName || ""} ${employee.lastName || ""}`.trim() ||
            "Unknown Employee"
          : "Unknown Employee";

        const calculations = processedPayslip.calculations || {};
        processedPayslips.push({
          employee: employeeName,
          netPay: calculations.netPay || 0,
          totalDeductions: calculations.totalDeductions || 0,
        });
      }
    } catch (error) {
      console.error("Error processing payslip:", error);
    }
  }

  return processedPayslips;
};

// Debug middleware for all payroll hub routes
router.use((req, res, next) => {
  next();
});

// GET route for payroll hub - Make more specific to avoid catching other routes
router.get(
  "/:companyCode/payrollhub",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {

    try {
      // Get company code from params
      const { companyCode } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        throw new Error("Company not found");
      }

      // Check for Xero integration
      const xeroIntegration = await Integration.findOne({
        company: company._id,
        provider: "xero",
        type: "accounting",
        status: "active",
      });

      // Check for QuickBooks integration
      const quickbooksIntegration = await Integration.findOne({
        company: company._id,
        provider: "quickbooks",
        type: "accounting",
        status: "active",
      });

      // Fetch EFT details for bank file generation testing
      const eftDetails = await EFTDetails.findOne({ company: company._id });


      // UPDATED: Fetch active payroll periods for the 2-step workflow
      // This includes periods that are "unfinished" or "active" in the payroll process

      // Step 1: Get periods that are still being reviewed (open or processing, not finalized)
      let reviewingPeriods = await PayrollPeriod.find({
        company: company._id,
        $or: [
          { status: "open", isFinalized: false },
          { status: "processing", isFinalized: false }
        ]
      })
        .select('_id employee frequency startDate endDate startDateBusiness endDateBusiness status isFinalized basicSalary grossPay totalDeductions netPay PAYE UIF SDL proratedPercentage workedDays totalDaysInPeriod isFirstPeriodWithDOA fullPeriodSalary')
        .populate({
          path: "employee",
          select: "firstName lastName employeeNumber payFrequency isUifExempt uifExemptReason",
        })
        .lean();

      // CRITICAL FIX: Check for periods with incorrect dates and recalculate
      console.log('🔧 CHECKING FOR PERIODS THAT NEED DATE CORRECTION AND RECALCULATION...');
      for (let i = 0; i < reviewingPeriods.length; i++) {
        const period = reviewingPeriods[i];

        // Check if period has wrong dates (starts on DOA instead of 1st of month for monthly periods)
        const hasWrongDates = (
          period.frequency === 'monthly' &&
          period.startDateBusiness &&
          !period.startDateBusiness.endsWith('-01') // Monthly periods should start on 1st
        );

        // Check if period needs financial recalculation
        const needsFinancialRecalculation = (
          (period.grossPay || 0) === 0 &&
          (period.PAYE || 0) === 0 &&
          (period.totalDeductions || 0) === 0 &&
          (period.basicSalary || 0) > 0
        );

        // Check if pro-rata calculation is wrong (30/30 instead of 30/31 for May)
        const hasWrongProRata = (
          period.frequency === 'monthly' &&
          period.totalDaysInPeriod === 30 &&
          period.endDateBusiness === '2025-05-31' // May should have 31 days
        );

        if (hasWrongDates || needsFinancialRecalculation || hasWrongProRata) {
          console.log(`  - Period ${period._id} needs correction:`, {
            hasWrongDates,
            needsFinancialRecalculation,
            hasWrongProRata,
            currentStart: period.startDateBusiness,
            currentEnd: period.endDateBusiness,
            currentTotalDays: period.totalDaysInPeriod
          });

          try {
            const recalculatedPeriod = await PayrollPeriod.recalculateExistingPeriod(period._id);
            if (recalculatedPeriod) {
              // Update the period in our array with the recalculated values
              reviewingPeriods[i] = {
                ...period,
                startDateBusiness: recalculatedPeriod.startDateBusiness,
                endDateBusiness: recalculatedPeriod.endDateBusiness,
                grossPay: recalculatedPeriod.grossPay,
                PAYE: recalculatedPeriod.PAYE,
                UIF: recalculatedPeriod.UIF,
                SDL: recalculatedPeriod.SDL,
                totalDeductions: recalculatedPeriod.totalDeductions,
                netPay: recalculatedPeriod.netPay,
                proratedPercentage: recalculatedPeriod.proratedPercentage,
                workedDays: recalculatedPeriod.workedDays,
                totalDaysInPeriod: recalculatedPeriod.totalDaysInPeriod,
                isFirstPeriodWithDOA: recalculatedPeriod.isFirstPeriodWithDOA,
                fullPeriodSalary: recalculatedPeriod.fullPeriodSalary
              };
              console.log(`  ✅ Period ${period._id} corrected:`, {
                newStart: recalculatedPeriod.startDateBusiness,
                newEnd: recalculatedPeriod.endDateBusiness,
                newTotalDays: recalculatedPeriod.totalDaysInPeriod,
                newWorkedDays: recalculatedPeriod.workedDays,
                newProRataPercentage: recalculatedPeriod.proratedPercentage
              });
            }
          } catch (recalcError) {
            console.error(`  ❌ Failed to recalculate period ${period._id}:`, recalcError);
          }
        }
      }

      // Step 2: Get periods that are finalized but don't have completed pay runs yet
      let finalizedPayslips = await PayrollPeriod.find({
        company: company._id,
        $or: [
          { status: "open", isFinalized: true },
          { status: "processing", isFinalized: true },
          { status: "finalized", isFinalized: true }
        ]
      })
        .select('_id employee frequency startDate endDate startDateBusiness endDateBusiness status isFinalized basicSalary grossPay totalDeductions netPay PAYE UIF SDL proratedPercentage workedDays totalDaysInPeriod isFirstPeriodWithDOA fullPeriodSalary')
        .populate({
          path: "employee",
          select: "firstName lastName employeeNumber payFrequency isUifExempt uifExemptReason",
        })
        .lean();

      // CRITICAL FIX: Check finalized periods for date/calculation issues
      console.log('🔧 CHECKING FINALIZED PERIODS FOR DATE CORRECTION AND RECALCULATION...');
      for (let i = 0; i < finalizedPayslips.length; i++) {
        const period = finalizedPayslips[i];

        // Same checks as reviewing periods
        const hasWrongDates = (
          period.frequency === 'monthly' &&
          period.startDateBusiness &&
          !period.startDateBusiness.endsWith('-01')
        );

        const needsFinancialRecalculation = (
          (period.grossPay || 0) === 0 &&
          (period.PAYE || 0) === 0 &&
          (period.totalDeductions || 0) === 0 &&
          (period.basicSalary || 0) > 0
        );

        const hasWrongProRata = (
          period.frequency === 'monthly' &&
          period.totalDaysInPeriod === 30 &&
          period.endDateBusiness === '2025-05-31'
        );

        if (hasWrongDates || needsFinancialRecalculation || hasWrongProRata) {
          console.log(`  - Finalized period ${period._id} needs correction`);
          try {
            const recalculatedPeriod = await PayrollPeriod.recalculateExistingPeriod(period._id);
            if (recalculatedPeriod) {
              finalizedPayslips[i] = {
                ...period,
                startDateBusiness: recalculatedPeriod.startDateBusiness,
                endDateBusiness: recalculatedPeriod.endDateBusiness,
                grossPay: recalculatedPeriod.grossPay,
                PAYE: recalculatedPeriod.PAYE,
                UIF: recalculatedPeriod.UIF,
                SDL: recalculatedPeriod.SDL,
                totalDeductions: recalculatedPeriod.totalDeductions,
                netPay: recalculatedPeriod.netPay,
                proratedPercentage: recalculatedPeriod.proratedPercentage,
                workedDays: recalculatedPeriod.workedDays,
                totalDaysInPeriod: recalculatedPeriod.totalDaysInPeriod,
                isFirstPeriodWithDOA: recalculatedPeriod.isFirstPeriodWithDOA,
                fullPeriodSalary: recalculatedPeriod.fullPeriodSalary
              };
              console.log(`  ✅ Finalized period ${period._id} corrected successfully`);
            }
          } catch (recalcError) {
            console.error(`  ❌ Failed to recalculate finalized period ${period._id}:`, recalcError);
          }
        }
      }

      // Combine both sets to create the complete active periods dataset
      // This represents all periods that are active in the 2-step workflow
      const pendingPayslips = [...reviewingPeriods, ...finalizedPayslips];


      // Remove duplicates from the combined dataset (in case a period appears in both queries)
      const uniquePendingPayslips = pendingPayslips.filter((period, index, self) =>
        index === self.findIndex(p => p._id.toString() === period._id.toString())
      );


      // Get all pay runs for the company
      const page = parseInt(req.query.page) || 1;
      const limit = 10;
      const skip = (page - 1) * limit;

      // Count total pay runs for pagination
      const totalPayRuns = await PayRun.countDocuments({ company: company._id });
      const totalPages = Math.ceil(totalPayRuns / limit);

      // Get pay runs with pagination
      const allPayRuns = await PayRun.find({ company: company._id })
        .select({
          startDate: 1,
          endDate: 1,
          status: 1,
          payslips: 1,
          totals: 1,
          totalAmount: 1,
          xeroSynced: 1,
          bankFile: 1,
          frequency: 1,
        })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      // Create pagination object
      const pagination = {
        page,
        limit,
        totalPages,
        totalItems: totalPayRuns,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      };

      // Calculate overall stats for each frequency
      const overallStats = {
        weekly: { unfinalizedCount: 0 },
        biweekly: { unfinalizedCount: 0 },
        monthly: { unfinalizedCount: 0 },
      };

      // Group payslips by frequency using the unique active periods
      const payslipsByFrequency = {};
      uniquePendingPayslips.forEach((payslip) => {
        const freq = payslip.frequency;
        if (!payslipsByFrequency[freq]) {
          payslipsByFrequency[freq] = [];
        }
        payslipsByFrequency[freq].push(payslip);
        // Only count unfinalized periods in the stats
        if (!payslip.isFinalized) {
          overallStats[freq].unfinalizedCount++;
        }
      });

      // Get pay runs grouped by frequency
      const payRunsByFrequency = {};
      allPayRuns.forEach((payRun) => {
        if (!payRunsByFrequency[payRun.frequency]) {
          payRunsByFrequency[payRun.frequency] = payRun;
        }
      });

      // IMPORTANT: Filter out periods that already have completed pay runs
      // This ensures we only show truly "active" periods in the 2-step workflow
      const activePeriodsOnly = uniquePendingPayslips.filter(period => {
        // Check if there's a completed pay run for this period
        const hasCompletedPayRun = allPayRuns.some(payRun => {
          const periodStart = new Date(period.startDate).toISOString().split('T')[0];
          const periodEnd = new Date(period.endDate).toISOString().split('T')[0];
          const payRunStart = new Date(payRun.startDate).toISOString().split('T')[0];
          const payRunEnd = new Date(payRun.endDate).toISOString().split('T')[0];

          return periodStart === payRunStart &&
                 periodEnd === payRunEnd &&
                 period.frequency === payRun.frequency &&
                 (payRun.status === 'finalized' || payRun.status === 'released');
        });

        // Only include periods that don't have completed pay runs
        return !hasCompletedPayRun;
      });


      // Recalculate payslips by frequency using only active periods
      const activePayslipsByFrequency = {};
      const activeOverallStats = {
        weekly: { unfinalizedCount: 0 },
        biweekly: { unfinalizedCount: 0 },
        monthly: { unfinalizedCount: 0 },
      };

      activePeriodsOnly.forEach((payslip) => {
        const freq = payslip.frequency;
        if (!activePayslipsByFrequency[freq]) {
          activePayslipsByFrequency[freq] = [];
        }
        activePayslipsByFrequency[freq].push(payslip);
        // Only count unfinalized periods in the stats
        if (!payslip.isFinalized) {
          activeOverallStats[freq].unfinalizedCount++;
        }
      });

      // Calculate currentStep based on active payslips' status
      const currentStep = activePeriodsOnly.every(
        (payslip) => payslip.isFinalized
      )
        ? 2
        : 1;

      // Debug: Log payslipsByFrequency structure before rendering
      console.log('🔍 SERVER-SIDE PAYSLIPS DATA DEBUG:');
      console.log('  - activePayslipsByFrequency keys:', Object.keys(activePayslipsByFrequency));
      Object.keys(activePayslipsByFrequency).forEach(freq => {
        const payslips = activePayslipsByFrequency[freq];
        console.log(`  - ${freq}: ${Array.isArray(payslips) ? payslips.length : 'not array'} payslips`);
        if (Array.isArray(payslips) && payslips.length > 0) {
          console.log(`    - Sample payslip structure:`, {
            id: payslips[0]._id,
            employeeId: payslips[0].employee,
            frequency: payslips[0].frequency,
            startDate: payslips[0].startDate,
            endDate: payslips[0].endDate,
            isFinalized: payslips[0].isFinalized,
            hasEmployee: !!payslips[0].employee,
            financialData: {
              basicSalary: payslips[0].basicSalary,
              grossPay: payslips[0].grossPay,
              totalDeductions: payslips[0].totalDeductions,
              netPay: payslips[0].netPay,
              PAYE: payslips[0].PAYE,
              UIF: payslips[0].UIF,
              SDL: payslips[0].SDL
            },
            proRataData: {
              proratedPercentage: payslips[0].proratedPercentage,
              workedDays: payslips[0].workedDays,
              totalDaysInPeriod: payslips[0].totalDaysInPeriod,
              isFirstPeriodWithDOA: payslips[0].isFirstPeriodWithDOA
            }
          });

          // ✅ REMOVED: PayrollService verification that was overriding BusinessDate results
          // The BusinessDate calculation in PayrollPeriod creation is now the single source of truth
          console.log('🔍 USING BUSINESSDATE CALCULATION RESULTS (no PayrollService override):');

          if (payslips.length > 0) {
            console.log('  - BusinessDate calculation stored in database:', {
              grossPay: payslips[0].grossPay,
              PAYE: payslips[0].PAYE,
              UIF: payslips[0].UIF,
              totalDeductions: payslips[0].totalDeductions,
              netPay: payslips[0].netPay,
              proratedPercentage: payslips[0].proratedPercentage + '%',
              workedDays: payslips[0].workedDays,
              totalDaysInPeriod: payslips[0].totalDaysInPeriod
            });
          }
        }
      });

      // Test JSON serialization
      try {
        const jsonString = JSON.stringify(activePayslipsByFrequency);
        console.log('  - JSON serialization successful, length:', jsonString.length);
        console.log('  - JSON preview (first 200 chars):', jsonString.substring(0, 200));
      } catch (jsonError) {
        console.error('  - JSON serialization failed:', jsonError.message);
      }

      // Render the template with all required variables
      res.render("payrollhub", {
        company,
        user: req.user,
        pendingPayslips: activePeriodsOnly, // Use only truly active periods for progress tracker
        originalPendingPayslips: activePeriodsOnly, // Keep same for consistency
        finalizedPayslips: activePeriodsOnly.filter(p => p.isFinalized), // Extract finalized from active periods
        payslipsByFrequency: activePayslipsByFrequency, // Use recalculated frequency groups
        allPayRuns,
        overallStats: activeOverallStats, // Use recalculated stats
        payRunsByFrequency,
        currentStep,
        moment: require("moment"),
        formatCurrency,
        formatPayPeriod,
        calculateGrossIncome,
        calculateDeductions,
        getProgressStatusIcon,
        csrfToken: req.csrfToken(),
        xeroIntegrationActive: !!xeroIntegration,
        quickbooksIntegrationActive: !!quickbooksIntegration,
        eftDetails: eftDetails, // For EFT configuration testing and bank file generation
        pagination,
      });
    } catch (error) {
      console.error("Error in payroll hub route:", error);
      req.flash("error", "Error loading payroll hub");
      return res.redirect("/dashboard");
    }
  }
);

// POST: Bulk finalize payroll periods
router.post(
  "/:companyCode/payroll/bulk-finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {

    const { companyCode } = req.params;
    const { payslipIds, frequency, generateNextPeriod, startDate, endDate } =
      req.body;


    try {
      // Find the company
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      // Validate payslip IDs
      if (
        !payslipIds ||
        !Array.isArray(payslipIds) ||
        payslipIds.length === 0
      ) {
        return res
          .status(400)
          .json({ error: "Please select payslips to finalize" });
      }

      // Remove duplicate payslip IDs
      const uniquePayslipIds = [...new Set(payslipIds)];

      // Query periods with populated employee data for next period generation
      const selectedPeriods = await PayrollPeriod.find({
        _id: { $in: uniquePayslipIds },
        company: company._id,
        frequency: frequency,
        startDate: {
          $gte: moment
            .tz(startDate, "Africa/Johannesburg")
            .startOf("day")
            .subtract(1, "day")
            .toDate(),
        },
        endDate: {
          $lte: moment.tz(endDate, "Africa/Johannesburg").endOf("day").toDate(),
        },
      }).populate({
        path: "employee",
        populate: {
          path: "payFrequency",
        },
      });

      if (selectedPeriods.length === 0) {
        return res.status(404).json({ error: "No matching periods found" });
      }


      // Filter for unfinalized periods
      const unfinalizedPeriods = selectedPeriods.filter(
        (p) => !p.isFinalized && p.status === "open"
      );

      // Track successful next period generations
      const generatedNextPeriods = [];

      // Check if any periods were previously finalized
      let message = "";
      let currentStep = 1;

      if (unfinalizedPeriods.length === 0) {
        // All selected periods are already finalized
        currentStep = 2; // Move to Create Pay Run step
        message = "Selected payslips are already finalized";
      } else {
        // Process each unfinalized period
        for (const period of unfinalizedPeriods) {
          // Finalize the period
          period.isFinalized = true;
          period.finalizedAt = new Date();
          period.finalizedBy = req.user._id;
          period.status = "finalized";

          // Save the updated period
          await period.save();


          // Generate next period if requested
          if (generateNextPeriod && period.employee) {
            try {

              // Make sure employee object is properly populated
              if (!period.employee.payFrequency) {
                await period.populate({
                  path: "employee",
                  populate: {
                    path: "payFrequency",
                  },
                });
              }

              // Generate the next period
              const nextPeriod = await PayrollPeriod.generateNextPeriod(
                period.employee,
                period
              );

              if (nextPeriod) {
                generatedNextPeriods.push(nextPeriod);
              } else {
              }
            } catch (error) {
              console.error(
                `Error generating next period for employee ${period.employee._id}:`,
                error
              );
            }
          }
        }

        // Set success message
        currentStep = 2; // Move to Create Pay Run step
        message = `Successfully finalized ${unfinalizedPeriods.length} payslips and generated ${generatedNextPeriods.length} new periods`;
      }

      // Check if all payslips for this frequency and period are now finalized
      const remainingUnfinalizedCount = await PayrollPeriod.countDocuments({
        company: company._id,
        frequency: frequency,
        startDate: { $gte: new Date(startDate) },
        endDate: { $lte: new Date(endDate) },
        isFinalized: false,
        status: "open",
      });


      res.json({
        success: true,
        message: message,
        currentStep: currentStep,
        allFinalized: remainingUnfinalizedCount === 0,
        count: unfinalizedPeriods.length,
        skippedCount: selectedPeriods.length - unfinalizedPeriods.length,
        periodIds: unfinalizedPeriods.map((p) => p._id),
        nextPeriods: generatedNextPeriods.map((p) => ({
          id: p._id,
          employeeId: p.employee,
          startDate: p.startDate,
          endDate: p.endDate,
          frequency: p.frequency,
        })),
      });
    } catch (error) {
      console.error("Error in bulk finalize:", error);
      res.status(500).json({
        error: "Failed to finalize payslips",
        details: error.message,
      });
    }
  }
);

// POST: Create a new pay run
router.post(
  "/:companyCode/payruns",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    const { companyCode } = req.params;
    const { frequency, startDate, endDate, forceCreate } = req.body;


    try {
      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      // Check for existing pay run in the same period
      const existingPayRun = await PayRun.findOne({
        company: company._id,
        frequency: frequency,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });

      if (existingPayRun) {
        return res.status(400).json({
          error: "A pay run already exists for this period",
          existingPayRun: {
            _id: existingPayRun._id,
            status: existingPayRun.status,
          },
        });
      }



      // DIRECT QUERY APPROACH: Query directly for finalized payslips without existing pay runs
      console.log('🔍 PAY RUN CREATION - DIRECT QUERY DEBUG:');
      console.log('  - Request params:', { frequency, startDate, endDate });
      console.log('  - Company ID:', company._id);
      console.log('  - Company Code:', companyCode);

      const finalizedQuery = {
        company: company._id,
        frequency: frequency,
        startDateBusiness: { $gte: startDate },
        endDateBusiness: { $lte: endDate },
        isFinalized: true,
        $or: [
          { payRun: { $exists: false } },  // No pay run reference
          { payRun: null }                 // Null pay run reference
        ]
      };

      console.log('🔍 DIRECT QUERY for finalized payslips:', JSON.stringify(finalizedQuery, null, 2));

      const finalizedPayslips = await PayrollPeriod.find(finalizedQuery).populate('employee');

      console.log('🔍 DIRECT QUERY RESULTS:');
      console.log('  - Found finalized payslips count:', finalizedPayslips.length);

      if (finalizedPayslips.length > 0) {
        finalizedPayslips.forEach((p, index) => {
          console.log(`  - Finalized Payslip ${index + 1}:`, {
            id: p._id,
            employee: p.employee?.firstName + ' ' + p.employee?.lastName,
            dates: `${p.startDateBusiness} to ${p.endDateBusiness}`,
            frequency: p.frequency,
            isFinalized: p.isFinalized,
            status: p.status,
            hasPayRun: !!p.payRun,
            payRunValue: p.payRun,
            grossPay: p.grossPay,
            netPay: p.netPay
          });
        });
      } else {
        console.log('  - No finalized payslips found with direct query');

        // Debug: Check what payslips exist for this company/frequency/date range
        const debugQuery = {
          company: company._id,
          frequency: frequency,
          startDateBusiness: { $gte: startDate },
          endDateBusiness: { $lte: endDate }
        };

        console.log('🔍 DEBUG: Checking all payslips in date range:', JSON.stringify(debugQuery, null, 2));

        const allPayslipsInRange = await PayrollPeriod.find(debugQuery)
          .select('startDateBusiness endDateBusiness isFinalized payRun status employee')
          .populate('employee', 'firstName lastName');

        console.log('🔍 DEBUG: All payslips in date range:', allPayslipsInRange.length);
        allPayslipsInRange.forEach((p, index) => {
          console.log(`  - All Payslip ${index + 1}:`, {
            id: p._id,
            employee: p.employee?.firstName + ' ' + p.employee?.lastName,
            dates: `${p.startDateBusiness} to ${p.endDateBusiness}`,
            frequency: p.frequency,
            isFinalized: p.isFinalized,
            status: p.status,
            hasPayRun: !!p.payRun,
            payRunValue: p.payRun
          });
        });

        // Debug: Check if any payslips exist for this company at all
        const anyPayslips = await PayrollPeriod.find({ company: company._id })
          .select('startDateBusiness endDateBusiness isFinalized frequency')
          .limit(5);

        console.log('🔍 DEBUG: Sample payslips for this company:', anyPayslips.map(p => ({
          id: p._id,
          dates: `${p.startDateBusiness} to ${p.endDateBusiness}`,
          frequency: p.frequency,
          isFinalized: p.isFinalized
        })));
      }

      // Since we're querying directly for finalized payslips, we don't need to check for unfinalized ones
      // The user has already confirmed they want to create a pay run with finalized payslips

      // Proceed with only finalized payslips
      if (finalizedPayslips.length === 0) {
        return res
          .status(400)
          .json({ error: "No finalized payslips found for this period" });
      }

      // Calculate period information
      const periodDate = new Date(startDate);
      const period =
        periodDate.toLocaleString("default", { month: "long" }) +
        " " +
        periodDate.getFullYear();
      const monthYear = startDate.slice(0, 7);

      // Calculate payment date (7 days after end date)
      const paymentDate = new Date(endDate);
      paymentDate.setDate(paymentDate.getDate() + 7);

      // Create the pay run
      const payRun = new PayRun({
        company: company._id,
        frequency: frequency,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        period: period,
        monthYear: monthYear,
        taxPeriod: monthYear,
        paymentDate: paymentDate,
        status: "finalized",
        payslips: finalizedPayslips.map((p) => p._id),
        totals: {
          grossPay: finalizedPayslips.reduce(
            (sum, p) => sum + (p.grossPay || 0),
            0
          ),
          totalDeductions: finalizedPayslips.reduce(
            (sum, p) => sum + (p.totalDeductions || 0),
            0
          ),
          netPay: finalizedPayslips.reduce(
            (sum, p) => sum + (p.netPay || 0),
            0
          ),
          employerContributions: 0,
        },
        createdBy: req.user._id,
        createdAt: new Date(),
      });

      await payRun.save();

      // Update payslips with pay run reference
      await PayrollPeriod.updateMany(
        { _id: { $in: finalizedPayslips.map((p) => p._id) } },
        { $set: { payRun: payRun._id } }
      );


      console.log('✅ PAY RUN CREATED SUCCESSFULLY:', {
        payRunId: payRun._id,
        period: payRun.period,
        totalAmount: payRun.totals.netPay
      });

      res.json({
        success: true,
        message: "Pay run created successfully",
        payRunId: payRun._id, // Frontend expects this field
        payRun: {
          _id: payRun._id,
          frequency: payRun.frequency,
          startDate: payRun.startDate,
          endDate: payRun.endDate,
          period: payRun.period,
          paymentDate: payRun.paymentDate,
          status: payRun.status,
          totals: payRun.totals,
        },
      });
    } catch (error) {
      console.error("Error creating pay run:", error);
      res.status(500).json({
        error: "Failed to create pay run",
        details: error.message,
      });
    }
  }
);

// GET: Get latest pay run for frequency
router.get(
  "/:companyCode/payruns/latest/:frequency",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    const { companyCode, frequency } = req.params;


    try {
      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      // Get the latest pay run for this frequency
      const payRun = await PayRun.findOne({
        company: company._id,
        frequency: frequency,
        status: "draft",
      }).sort({ createdAt: -1 });


      if (!payRun) {
        return res
          .status(404)
          .json({ error: "No pay run found for this frequency" });
      }

      res.json({
        success: true,
        payRun: payRun,
      });
    } catch (error) {
      console.error("Error getting latest pay run:", error);
      res.status(500).json({
        error: "Failed to get latest pay run",
        details: error.message,
      });
    }
  }
);

// POST: Finalize a pay run
router.post(
  "/:companyCode/payruns/:payRunId/finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {

    try {
      const { companyCode, payRunId } = req.params;

      const payRun = await PayRun.findById(payRunId).populate("company");
      if (!payRun) {
        console.error("Pay run not found:", payRunId);
        req.flash("error", "Pay run not found");
        return res.redirect("/dashboard");
      }

      // Fetch all payslips for this pay run
      const payslips = await PayrollPeriod.find({
        company: payRun.company,
        startDate: payRun.startDate,
        endDate: payRun.endDate,
      }).populate({
        path: "employee",
        populate: {
          path: "payFrequency",
        },
      });


      // Process each payslip
      for (const payslip of payslips) {
        try {

          // Validate employee data
          if (!payslip.employee) {
            console.error("Payslip has no associated employee:", payslip._id);
            continue;
          }

          // Calculate final amounts
          const payrollData = await Payroll.findOne({
            employee: payslip.employee._id,
            month: payRun.endDate,
          });

          let taxableIncome = 0;
          let totalDeductions = 0;

          if (payrollData) {
            taxableIncome = calculateTotalTaxableIncomeWithLogging(payrollData);

            totalDeductions = calculateTotalDeductionsWithLogging({
              payrollData,
              taxableIncome,
              frequency: payslip.employee.payFrequency?.frequency || "monthly",
              startDate: payRun.startDate,
              endDate: payRun.endDate,
              employee: payslip.employee,
            });
          } else if (payslip.payrollPeriod) {
            // Try to get data from the payroll period
            const payrollPeriod = await PayrollPeriod.findById(payslip.payrollPeriod);
            
            if (payrollPeriod) {
              taxableIncome = payrollPeriod.grossPay || 0;
              totalDeductions = payrollPeriod.totalDeductions || 0;
            } else {
              taxableIncome = payslip.grossPay || 0;
              totalDeductions = payslip.totalDeductions || 0;
            }
          } else {
            taxableIncome = payslip.grossPay || 0;
            totalDeductions = payslip.totalDeductions || 0;
          }

          // Update payslip with final calculations
          payslip.calculations = {
            basicSalary: {
              full: payrollData?.basicSalary || payslip.basicSalary || 0,
              prorated: taxableIncome,
            },
            totalIncome: taxableIncome,
            deductions: {
              statutory: {
                paye: calculatePAYEImproved({
                  annualSalary: taxableIncome * (payslip.employee.payFrequency?.frequency === 'weekly' ? 52 :
                                                payslip.employee.payFrequency?.frequency === 'biweekly' ? 26 : 12),
                  frequency:
                    payslip.employee.payFrequency?.frequency || "monthly",
                  proRataPercentage: payslip.proratedPercentage || 100,
                }),
                uif: calculateUIF({
                  salary: taxableIncome,
                  employee: payslip.employee,
                  startDate: payRun.startDate,
                  endDate: payRun.endDate,
                }),
              },
            },
            totalDeductions,
            netPay: taxableIncome - totalDeductions,
          };

          // Store tax values in the PayrollPeriod model
          const paye = payslip.calculations.deductions.statutory.paye || 0;
          const uif = payslip.calculations.deductions.statutory.uif || 0;
          const sdl = taxableIncome * 0.01; // SDL is typically 1% of gross pay
          
          payslip.PAYE = paye;
          payslip.UIF = uif;
          payslip.SDL = sdl;

          // Update payslip status
          payslip.status = "finalized";
          payslip.isFinalized = true;
          payslip.finalizedAt = new Date();
          payslip.finalizedBy = req.user._id;
          
          // Ensure core payslip fields are updated
          payslip.grossPay = taxableIncome;
          payslip.totalDeductions = totalDeductions;
          payslip.netPay = taxableIncome - totalDeductions;

          await payslip.save();
        } catch (error) {
          console.error("Error processing payslip:", error);
          throw error;
        }
      }

      // Update pay run status
      payRun.status = "finalized";
      payRun.finalizedAt = new Date();
      payRun.finalizedBy = req.user._id;

      await payRun.save();

      req.flash("success", "Pay run has been finalized successfully");
      return res.redirect(`/clients/${companyCode}/payrollhub`);
    } catch (error) {
      console.error("Error in pay run finalization:", error);
      req.flash("error", "An error occurred while finalizing the pay run");
      return res.redirect("/dashboard");
    }
  }
);

// Bank file generation route
router.post('/:companyCode/payruns/:payRunId/bank-file/standard', async (req, res) => {
    const { companyCode, payRunId } = req.params;
    const { actionDate } = req.body;
    const userId = req.user.id;

    console.log('\n=== ENHANCED Bank File Generation ===');
    console.log('Request parameters:', { companyCode, payRunId, actionDate, userId });

    try {
        // Find company
        const company = await Company.findOne({ companyCode });
        if (!company) {
            throw new Error('Company not found');
        }
        console.log('Company found:', { id: company._id, name: company.name });

        // Find company's EFT details and configuration
        const eftDetails = await EFTDetails.findOne({ company: company._id });
        if (!eftDetails) {
            throw new Error('EFT configuration not found. Please configure EFT settings first.');
        }
        console.log('EFT Details found:', {
            format: eftDetails.eftFormat,
            bank: eftDetails.bank,
            accountNumber: eftDetails.accountNumber ? '***' + eftDetails.accountNumber.slice(-4) : 'Not set'
        });

        // Find pay run (PERIOD-SPECIFIC FILTERING)
        const rawPayRun = await PayRun.findOne({
            _id: payRunId,
            company: company._id
        });

        if (!rawPayRun) {
            throw new Error('Pay run not found');
        }
        console.log('Pay run found:', {
            id: rawPayRun._id,
            status: rawPayRun.status,
            payslipsCount: rawPayRun.payslips?.length || 0,
            period: `${rawPayRun.startDate} to ${rawPayRun.endDate}`
        });

        // Find payroll periods (PERIOD-SPECIFIC: Only from this pay run)
        const payslips = await PayrollPeriod.find({
            _id: { $in: rawPayRun.payslips }
        });
        console.log('Payslips found for this pay run:', payslips.length);

        if (payslips.length === 0) {
            throw new Error('No payslips found for this pay run');
        }

        // Get employee details for each payslip
        const employeeIds = payslips.map(p => p.employee);

        const employees = await Employee.find(
            { _id: { $in: employeeIds } },
            {
                firstName: 1,
                lastName: 1,
                bank: 1,
                accountType: 1,
                accountNumber: 1,
                branchCode: 1,
                holderRelationship: 1,
                accountHolder: 1
            }
        );
        console.log('Employees found:', employees.length);


        // Create a map of employee details
        const employeeMap = employees.reduce((map, emp) => {
            map[emp._id.toString()] = emp;
            return map;
        }, {});

        const detailedPayslips = payslips.map((payslip, index) => {
            const employee = employeeMap[payslip.employee.toString()];
            return {
                payslip,
                employee
            };
        });

        // Validate bank details and create transactions
        const transactions = [];
        const invalidPayslips = [];

        for (const { payslip, employee } of detailedPayslips) {
            const validation = {
                hasEmployee: !!employee,
                hasBankDetails: !!(employee?.bank && employee?.accountNumber && employee?.branchCode),
                hasAccountNumber: !!employee?.accountNumber,
                hasBankName: !!employee?.bank,
                hasAccountType: !!employee?.accountType,
                isValid: employee?.branchCode
            };
            

            if (validation.hasEmployee && validation.hasBankDetails) {
                const transaction = {
                    branchCode: employee.branchCode,
                    accountNumber: employee.accountNumber,
                    amount: payslip.netPay,
                    employeeName: `${employee.firstName} ${employee.lastName}`
                };
                transactions.push(transaction);
            } else {
                invalidPayslips.push({
                    employeeId: payslip.employee,
                    employeeName: employee ? `${employee.firstName} ${employee.lastName}` : 'Unknown',
                    missingDetails: {
                        bankName: !validation.hasBankName,
                        accountNumber: !validation.hasAccountNumber,
                        branchCode: !validation.isValid,
                        accountType: !validation.hasAccountType
                    }
                });
            }
        }


        if (transactions.length === 0) {
            throw new Error('No valid payslips found with complete bank details');
        }

        if (invalidPayslips.length > 0) {
            throw new Error(JSON.stringify({
                type: 'MISSING_BANK_DETAILS',
                message: 'Some employees are missing bank details',
                details: invalidPayslips
            }));
        }

        console.log('Valid transactions for bank file:', transactions.length);

        // EFT FORMAT-BASED BANK FILE GENERATION
        const { mapEftFormatToBankFileFormat } = require('../utils/eftSettingsUtils');
        const bankFileFormat = mapEftFormatToBankFileFormat(eftDetails.eftFormat);

        console.log('EFT Format Mapping:', {
            originalFormat: eftDetails.eftFormat,
            mappedFormat: bankFileFormat
        });

        let bankFile;
        let fileExtension = 'txt';
        let contentType = 'text/plain';

        // Generate bank file based on EFT format
        switch (bankFileFormat) {
            case 'fnb':
                console.log('Generating FNB format bank file...');
                const { generateFNBBankFile } = require('../utils/bankFileUtils');
                bankFile = await generateFNBBankFile(company, transactions, actionDate || new Date());
                fileExtension = 'txt';
                break;

            case 'absa':
                console.log('Generating ABSA format bank file...');
                // TODO: Implement ABSA bank file generator
                const { generateABSABankFile } = require('../utils/bankFileUtils');
                if (typeof generateABSABankFile === 'function') {
                    bankFile = await generateABSABankFile(company, transactions, eftDetails, actionDate || new Date());
                } else {
                    // Fallback to FNB format with warning
                    console.warn('ABSA format not implemented, falling back to FNB format');
                    const { generateFNBBankFile } = require('../utils/bankFileUtils');
                    bankFile = await generateFNBBankFile(company, transactions, actionDate || new Date());
                }
                fileExtension = eftDetails.eftFormat.includes('.csv') ? 'csv' : 'txt';
                contentType = eftDetails.eftFormat.includes('.csv') ? 'text/csv' : 'text/plain';
                break;

            case 'standard':
                console.log('Generating Standard Bank format bank file...');
                // TODO: Implement Standard Bank file generator
                const { generateStandardBankFile } = require('../utils/bankFileUtils');
                if (typeof generateStandardBankFile === 'function') {
                    bankFile = await generateStandardBankFile(company, transactions, eftDetails, actionDate || new Date());
                } else {
                    // Fallback to FNB format with warning
                    console.warn('Standard Bank format not implemented, falling back to FNB format');
                    const { generateFNBBankFile } = require('../utils/bankFileUtils');
                    bankFile = await generateFNBBankFile(company, transactions, actionDate || new Date());
                }
                fileExtension = 'txt';
                break;

            case 'nedbank':
                console.log('Generating Nedbank format bank file...');
                // TODO: Implement Nedbank file generator
                const { generateNedbankBankFile } = require('../utils/bankFileUtils');
                if (typeof generateNedbankBankFile === 'function') {
                    bankFile = await generateNedbankBankFile(company, transactions, eftDetails, actionDate || new Date());
                } else {
                    // Fallback to FNB format with warning
                    console.warn('Nedbank format not implemented, falling back to FNB format');
                    const { generateFNBBankFile } = require('../utils/bankFileUtils');
                    bankFile = await generateFNBBankFile(company, transactions, actionDate || new Date());
                }
                fileExtension = eftDetails.eftFormat.includes('CSV') ? 'csv' : 'txt';
                contentType = eftDetails.eftFormat.includes('CSV') ? 'text/csv' : 'text/plain';
                break;

            default:
                console.log('Unknown format, defaulting to FNB...');
                const { generateFNBBankFile: defaultFNB } = require('../utils/bankFileUtils');
                bankFile = await defaultFNB(company, transactions, actionDate || new Date());
                fileExtension = 'txt';
                break;
        }

        // Set the filename with proper format indication
        const formatPrefix = bankFileFormat.toUpperCase();
        const filename = `${formatPrefix}_bank_file_${companyCode}_${moment().format('YYYYMMDD_HHmmss')}.${fileExtension}`;

        console.log('Generated bank file:', {
            format: bankFileFormat,
            filename: filename,
            size: bankFile ? bankFile.length : 0
        });

        // Send the response
        res.setHeader('Content-Type', contentType);
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.send(bankFile);

    } catch (error) {
        console.error('Error generating bank file:', error);
        console.error('Error stack:', error.stack);
        res.status(500).json({
            error: 'Error generating bank file',
            details: error.message
        });
    }
});

// Get EFT format for a specific pay run
router.get(
  "/payrun/:payRunId/eft-format",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { payRunId } = req.params;
      const { currentCompany } = req.user;

      // Find the pay run
      const payRun = await PayRun.findById(payRunId);
      if (!payRun) {
        return res.status(404).json({ error: "Pay run not found" });
      }

      // Get or create EFT settings for the company
      const eftSettings = await getOrCreateEFTSettings(currentCompany);

      // Update the pay run with the EFT settings
      payRun.eftSettings = eftSettings._id;
      await payRun.save();

      // Return the EFT settings
      res.json({
        bankFileFormat: eftSettings.bankFileFormat,
        bankName: eftSettings.bankName,
        branchCode: eftSettings.branchCode,
        accountNumber: eftSettings.accountNumber,
      });
    } catch (error) {
      console.error("Error retrieving EFT format:", error);
      res.status(500).json({ error: "Failed to retrieve EFT format" });
    }
  }
);

// Route to finalize a single payslip
router.post(
  "/:companyCode/payroll/finalize-payslip/:payslipId",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {

    try {
      const { companyCode, payslipId } = req.params;

      // Find the company
      const company = await Company.findOne({ companyCode });

      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Find the payslip period
      const payrollPeriod = await PayrollPeriod.findById(payslipId).populate(
        "employee"
      );

      if (!payrollPeriod) {
        return res.status(404).json({
          success: false,
          message: "Payroll period not found",
        });
      }

      // SURGICAL FIX: Calculate annual salary based on actual frequency
      const periodsPerYear = payrollPeriod.employee?.payFrequency?.frequency === 'weekly' ? 52 :
                            payrollPeriod.employee?.payFrequency?.frequency === 'biweekly' ? 26 : 12;
      const annualSalary = payrollPeriod.basicSalary * periodsPerYear;
      const payeResult = await calculatePAYEImproved({
        annualSalary,
        frequency: payrollPeriod.employee?.payFrequency?.frequency || payrollPeriod.frequency || "monthly",
        proRataPercentage: payrollPeriod.proratedPercentage || 100,
      });

      // Calculate UIF (1% of basic salary, capped at R177.12)
      const uif = Math.min(payrollPeriod.basicSalary * 0.01, 177.12);

      // Set the amounts and finalization flags
      payrollPeriod.grossPay = payrollPeriod.basicSalary;
      payrollPeriod.totalDeductions = payeResult + uif;
      payrollPeriod.netPay =
        payrollPeriod.grossPay - payrollPeriod.totalDeductions;

      // Store tax values in the PayrollPeriod model
      payrollPeriod.PAYE = payeResult;
      payrollPeriod.UIF = uif;
      payrollPeriod.SDL = payrollPeriod.grossPay * 0.01; // SDL is typically 1% of gross pay

      // Set finalization flags
      payrollPeriod.isFinalized = true;
      payrollPeriod.finalizedAt = new Date();
      payrollPeriod.finalizedBy = req.user._id;
      payrollPeriod.status = "finalized";


      await payrollPeriod.save();

      // Get fully populated employee object
      const populatedEmployee = await Employee.findById(
        payrollPeriod.employee._id
      )
        .populate("payFrequency")
        .populate("company");

      if (!populatedEmployee) {
        req.flash("error", "Employee not found");
        return res.redirect(`/clients/${req.params.companyCode}/payrollhub`);
      }

      if (!populatedEmployee.payFrequency) {
        req.flash("error", "Employee pay frequency not configured");
        return res.redirect(`/clients/${req.params.companyCode}/payrollhub`);
      }

      // Generate next period
      await PayrollPeriod.generateNextPeriod(populatedEmployee, payrollPeriod);

      req.flash("success", "Payslip finalized successfully");
      res.redirect(`/clients/${req.params.companyCode}/payrollhub`);
    } catch (error) {
      console.error("Error finalizing payslip:", error);
      req.flash("error", error.message || "Failed to finalize payslip");
      res.redirect(`/clients/${req.params.companyCode}/payrollhub`);
    }
  }
);

// Route to finalize payslips by frequency
router.post(
  "/payroll/finalize-payslips/:frequency",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {

    try {
      const { payslipIds } = req.body;
      const { frequency } = req.params;

      if (
        !payslipIds ||
        !Array.isArray(payslipIds) ||
        payslipIds.length === 0
      ) {
        return res.status(400).json({
          success: false,
          message: "No payslip IDs provided",
        });
      }

      // Find all payslips
      const payslips = await PayrollPeriod.find({
        _id: { $in: payslipIds },
        frequency: frequency,
        isFinalized: false,
      }).populate("employee");

      if (!payslips || payslips.length === 0) {
        return res.status(404).json({
          success: false,
          message: "No valid payslips found to finalize",
        });
      }

      // Start a transaction
      const session = await mongoose.startSession();
      session.startTransaction();

      try {
        // Update all payslips
        for (const payslip of payslips) {
          // SURGICAL FIX: Calculate annual salary based on actual frequency
          const periodsPerYear = payslip.employee?.payFrequency?.frequency === 'weekly' ? 52 :
                                payslip.employee?.payFrequency?.frequency === 'biweekly' ? 26 : 12;
          const annualSalary = payslip.basicSalary * periodsPerYear;
          const payeResult = await calculatePAYEImproved({
            annualSalary,
            frequency: payslip.employee?.payFrequency?.frequency || payslip.frequency || "monthly",
            proRataPercentage: payslip.proratedPercentage || 100,
          });

          // Calculate UIF (1% of basic salary, capped at R177.12)
          const uif = Math.min(payslip.basicSalary * 0.01, 177.12);

          // Set the amounts and finalization flags
          payslip.grossPay = payslip.basicSalary;
          payslip.totalDeductions = payeResult + uif;
          payslip.netPay = payslip.grossPay - payslip.totalDeductions;

          // Store tax values in the PayrollPeriod model
          payslip.PAYE = payeResult;
          payslip.UIF = uif;
          payslip.SDL = payslip.grossPay * 0.01; // SDL is typically 1% of gross pay

          // Set finalization flags
          payslip.isFinalized = true;
          payslip.finalizedAt = new Date();
          payslip.finalizedBy = req.user._id;
          payslip.status = "finalized";

          await payslip.save({ session });

          // Generate next period if needed
          const employee = await Employee.findById(payslip.employee._id)
            .populate("payFrequency")
            .populate("company")
            .session(session);

          if (employee && employee.status === "Active") {
            try {
              await PayrollService.generateNextPeriod(employee, payslip);
            } catch (error) {
              console.error("Error generating next period:", error);
            }
          }
        }

        // Commit the transaction
        await session.commitTransaction();

        res.json({
          success: true,
          message: `Successfully finalized ${payslips.length} payslips`,
          count: payslips.length,
        });
      } catch (error) {
        // Rollback the transaction on error
        await session.abortTransaction();
        throw error;
      } finally {
        session.endSession();
      }
    } catch (error) {
      console.error("Error finalizing payslips:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to finalize payslips",
      });
    }
  }
);

// Route to manually finalize a pay run (for progress tracker)
router.post(
  "/:companyCode/payruns/:payRunId/manual-finalize",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {

    try {
      const { companyCode, payRunId } = req.params;
      const payRun = await PayRun.findById(payRunId).populate("company");

      if (!payRun) {
        return res.status(404).json({ error: "Pay run not found" });
      }

      if (payRun.company.companyCode !== companyCode) {
        return res.status(403).json({ error: "Company code mismatch" });
      }

      // Update the pay run to mark it as manually finalized
      payRun.manuallyFinalized = true;
      await payRun.save();

      // Create audit log
      const auditLog = new AuditLog({
        user: req.user._id,
        company: payRun.company._id, // Add the company ID
        action: "manual_finalize_pay_run",
        details: {
          payRunId: payRun._id,
          frequency: payRun.frequency,
          companyCode,
        },
      });
      await auditLog.save();

      res.status(200).json({
        success: true,
        message: "Pay run manually finalized successfully",
        frequency: payRun.frequency,
      });
    } catch (error) {
      console.error("Error manually finalizing pay run:", error);
      res.status(500).json({
        error: "Failed to manually finalize pay run",
        details: error.message,
      });
    }
  }
);

// GET route for payslips by frequency
router.get(
  "/:companyCode/payroll/payslips/:frequency",
  ensureAuthenticated,
  async (req, res) => {
    try {

      const { companyCode, frequency } = req.params;
      const { startDate, endDate } = req.query;

      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }


      // Find existing payroll periods first
      let payrollPeriods = await PayrollPeriod.find({
        company: company._id,
        frequency: frequency,
      }).populate("employee");


      // Filter periods by exact date range matching
      // Only include payslips that belong to the exact period being requested
      payrollPeriods = payrollPeriods.filter((period) => {
        const periodStart = moment(period.startDate);
        const periodEnd = moment(period.endDate);
        const queryStart = moment(startDate);
        const queryEnd = moment(endDate);

        // Strict period matching: period dates must exactly match the requested period
        // This ensures June modal only shows June payslips, May modal only shows May payslips, etc.
        return (
          periodStart.isSame(queryStart, "day") &&
          periodEnd.isSame(queryEnd, "day")
        );
      });


      // If no periods found, find active employees to create periods for
      if (payrollPeriods.length === 0) {

        const employees = await Employee.find({
          company: company._id,
          isActive: true,
        });


        if (employees.length > 0) {
          const newPeriods = [];
          for (const employee of employees) {

            // Calculate tax deductions for the period
            const basicSalary = employee.basicSalary || 0;
            let paye = 0;
            let uif = 0;
            let totalDeductions = 0;
            let netPay = basicSalary;

            // ✅ FIXED: Use BusinessDate.calculateProratedSalary() (same as employeeProfile)
            const BusinessDate = require('../utils/BusinessDate');

            // Convert period dates to BusinessDate format
            let periodStartStr = BusinessDate.normalize(startDate);
            let periodEndStr = BusinessDate.normalize(endDate);

            // SURGICAL FIX: For weekly employees, ensure correct 7-day period calculation
            if (employee.payFrequency?.frequency === 'weekly') {
              // For weekly periods, always calculate proper 7-day period
              // Period end is correct, calculate start as exactly 7 days before (6 days back)
              const endDate = BusinessDate.normalize(periodEndStr);
              periodStartStr = BusinessDate.addDays(endDate, -6);

              console.log('Weekly period correction applied (payrollhub):', {
                originalStart: BusinessDate.normalize(startDate),
                originalEnd: periodEndStr,
                correctedStart: periodStartStr,
                correctedEnd: periodEndStr,
                explanation: 'Weekly periods must be exactly 7 days'
              });
            }

            // Calculate proration using the same method as employeeProfile
            const proratedResult = BusinessDate.calculateProratedSalary(
              employee.doa,
              basicSalary,
              employee,
              periodStartStr,
              periodEndStr
            );

            console.log('🔍 BusinessDate proration calculation for payrollhub:', {
              employeeDOA: employee.doa,
              periodStart: periodStartStr,
              periodEnd: periodEndStr,
              basicSalary,
              proratedPercentage: proratedResult.proratedPercentage.toFixed(2) + '%',
              proratedSalary: proratedResult.proratedSalary,
              isFirstPeriodWithDOA: proratedResult.isFirstPeriodWithDOA
            });

            if (basicSalary > 0) {
              try {
                // SURGICAL FIX: Calculate PAYE using enhanced method with correct frequency
                const periodsPerYear = employee.payFrequency?.frequency === 'weekly' ? 52 :
                                      employee.payFrequency?.frequency === 'biweekly' ? 26 : 12;
                const annualSalary = basicSalary * periodsPerYear;
                // SURGICAL FIX: Use enhanced PAYE calculation method (same as Calculator card)
                const payrollCalculations = require('../utils/payrollCalculations');
                const payeCalculation = payrollCalculations.calculateEnhancedPAYE({
                  annualSalary,
                  age: payrollCalculations.calculateAge(employee.dob || new Date()),
                  frequency: employee.payFrequency?.frequency || 'monthly'
                });

                // Apply proration to the period-specific PAYE
                const periodPAYE = payeCalculation.periodPAYE || 0;
                const shouldApplyProRata = proratedResult.isFirstPeriodWithDOA && proratedResult.proratedPercentage < 100;
                paye = shouldApplyProRata
                  ? (periodPAYE * proratedResult.proratedPercentage) / 100
                  : periodPAYE;

                // ✅ FIXED: Calculate UIF on prorated amount (same as employeeProfile)
                const proRatedAmount = parseFloat(proratedResult.proratedSalary);
                uif = Math.min(proRatedAmount * 0.01, 177.12);

                // Calculate SDL for employer reporting (NOT included in employee deductions)
                const sdl = proRatedAmount * 0.01; // 1% of prorated amount

                // Calculate totals (EXCLUDING SDL - it's an employer contribution)
                totalDeductions = paye + uif;
                netPay = proRatedAmount - totalDeductions;

                console.log(`PayrollPeriod creation - Employee ${employee._id}:`, {
                  basicSalary,
                  proRatedAmount: proRatedAmount.toFixed(2),
                  proratedPercentage: proratedResult.proratedPercentage.toFixed(2) + '%',
                  paye: paye.toFixed(2),
                  uif: uif.toFixed(2),
                  sdl: `${sdl.toFixed(2)} (employer contribution - NOT deducted from employee)`,
                  totalDeductions: `${totalDeductions.toFixed(2)} (excludes SDL)`,
                  netPay: netPay.toFixed(2)
                });

              } catch (error) {
                console.error(`Error calculating taxes for employee ${employee._id}:`, error);
                // Continue with zero values if calculation fails
              }
            }

            // Fix period end date calculation
            // If endDate is the first day of next month, convert it to last day of current month
            let correctedEndDate = moment(endDate);
            if (correctedEndDate.date() === 1) {
              // This is likely the first day of next month, convert to last day of previous month
              correctedEndDate = correctedEndDate.subtract(1, 'day').endOf('day');
              console.log(`Corrected end date from ${endDate} to ${correctedEndDate.format('YYYY-MM-DD')}`);
            } else {
              correctedEndDate = correctedEndDate.endOf('day');
            }

            const period = new PayrollPeriod({
              employee: employee._id,
              company: company._id,
              frequency: frequency,
              startDate: moment(startDate).startOf("day").toDate(),
              endDate: correctedEndDate.toDate(),
              basicSalary: basicSalary,
              grossPay: parseFloat(proratedResult.proratedSalary), // ✅ FIXED: Use BusinessDate prorated amount
              PAYE: paye,
              UIF: uif,
              SDL: sdl, // Store SDL for employer reporting (NOT in totalDeductions)
              totalDeductions: totalDeductions, // Excludes SDL
              netPay: netPay,
              // ✅ FIXED: Add BusinessDate proration fields (same as employeeProfile)
              proratedPercentage: proratedResult.proratedPercentage,
              workedDays: proratedResult.workedDays,
              totalDaysInPeriod: proratedResult.totalDaysInPeriod,
              isFirstPeriodWithDOA: proratedResult.isFirstPeriodWithDOA,
              status: "open",
              isFinalized: false,
            });
            newPeriods.push(period);
          }

          // Save all new periods
          payrollPeriods = await PayrollPeriod.insertMany(newPeriods);

          // Populate employee data
          payrollPeriods = await PayrollPeriod.find({
            _id: { $in: payrollPeriods.map((p) => p._id) },
          }).populate("employee");

        }
      }

      // Calculate totals from already calculated period values
      let totalGross = 0;
      let totalDeductions = 0;
      let totalNet = 0;

      // Sum up the already calculated values from each period
      for (const period of payrollPeriods) {
        const grossPay = period.grossPay || 0;
        const deductions = period.totalDeductions || 0;
        const netPay = period.netPay || 0;

        totalGross += grossPay;
        totalDeductions += deductions;
        totalNet += netPay;
      }

      const totals = {
        totalGross,
        totalDeductions,
        totalNet
      };


      // Format response with enhanced employee and period information
      // CRITICAL: Prioritize BusinessDate strings for accurate display without timezone issues
      const formattedPayslips = payrollPeriods.map((period) => {
        console.log(`📅 Processing period ${period._id} dates:`, {
          startDate: period.startDate,
          endDate: period.endDate,
          startDateBusiness: period.startDateBusiness,
          endDateBusiness: period.endDateBusiness
        });

        return {
          _id: period._id,
          // Employee information
          employeeName: period.employee
            ? `${period.employee.firstName} ${period.employee.lastName}`
            : "Unknown Employee",
          employee: period.employee ? {
            firstName: period.employee.firstName,
            lastName: period.employee.lastName,
            employeeNumber: period.employee.employeeNumber
          } : null,
          employeeNumber: period.employee?.employeeNumber || "N/A",
          // Financial information
          grossPay: period.grossPay || 0,
          totalDeductions: period.totalDeductions || 0,
          netPay: period.netPay || 0,
          PAYE: period.PAYE || 0,
          UIF: period.UIF || 0,
          // Period information - PRIORITIZE BusinessDate strings for display
          startDate: period.startDate,
          endDate: period.endDate,
          startDateBusiness: period.startDateBusiness,
          endDateBusiness: period.endDateBusiness,
          // Add formatted display dates to avoid frontend timezone conversion
          startDateDisplay: period.startDateBusiness || (period.startDate ? moment(period.startDate).format('YYYY-MM-DD') : null),
          endDateDisplay: period.endDateBusiness || (period.endDate ? moment(period.endDate).format('YYYY-MM-DD') : null),
          // Status information
          isFinalized: period.isFinalized || false,
          status: period.status || 'open',
          frequency: period.frequency
        };
      });


      res.json({
        success: true,
        payslips: formattedPayslips,
        totals,
      });
    } catch (error) {
      console.error("Error fetching payslips:", error);
      res.status(500).json({
        success: false,
        message: "Error fetching payslips",
        error: error.message,
      });
    }
  }
);

// GET: Get finalized payslips for a specific frequency and period
router.get(
  "/:companyCode/payroll/finalized-payslips/:frequency",
  ensureAuthenticated,
  async (req, res) => {
    const { companyCode, frequency } = req.params;
    const { startDate, endDate } = req.query;


    try {
      // Find the company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      // Build query for finalized payslips
      const query = {
        company: company._id,
        frequency: frequency,
        isFinalized: true,
      };

      // Add date filters if provided
      if (startDate && endDate) {
        query.startDate = { $gte: new Date(startDate) };
        query.endDate = { $lte: new Date(endDate) };
      }


      // Find payslips
      const payslips = await PayrollPeriod.find(query);

      // Return the payslip IDs with success flag

      res.json({
        success: true,
        payslipIds: payslips.map((p) => p._id),
        count: payslips.length,
      });
    } catch (error) {
      console.error("Error getting finalized payslips:", error);
      res.status(500).json({
        error: "Failed to get finalized payslips",
        details: error.message,
      });
    }
  }
);

// POST route to send payroll data to Xero
router.post(
  "/:companyCode/pay-runs/:payRunId/xero",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, payRunId } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        throw new Error("Company not found");
      }

      // Find pay run
      const payRun = await PayRun.findById(payRunId)
        .populate("payslips")
        .populate("company");

      if (!payRun) {
        throw new Error("Pay run not found");
      }

      // Check if already synced
      if (payRun.xeroSynced) {
        return res.status(400).json({
          success: false,
          message: "This pay run has already been synced to Xero",
        });
      }

      // Check for active Xero integration
      const integration = await Integration.findOne({
        company: company._id,
        provider: "xero",
        type: "accounting",
        status: "active",
      });

      if (!integration) {
        throw new Error("No active Xero integration found");
      }

      // Get account mappings
      const mappings = await XeroAccountMapping.find({
        company: company._id,
      });

      if (!mappings.length) {
        throw new Error("No Xero account mappings configured");
      }

      // Calculate totals
      const totals = {
        basicSalary: payRun.payslips.reduce(
          (sum, p) => sum + (p.basicSalary || 0),
          0
        ),
        paye: payRun.payslips.reduce((sum, p) => sum + (p.PAYE || 0), 0),
        uif: payRun.payslips.reduce((sum, p) => sum + (p.UIF || 0), 0),
        sdl: payRun.payslips.reduce((sum, p) => sum + (p.SDL || 0), 0),
      };

      // Prepare data for Xero sync
      const syncData = {
        payrollId: payRun._id,
        payrollData: {
          basicSalary: totals.basicSalary,
          paye: totals.paye,
          uif: totals.uif,
          sdl: totals.sdl,
          employeeCount: payRun.payslips.length,
          period: `${moment(payRun.startDate).format("MMMM YYYY")} Payroll`,
        },
        description: `Payroll for ${moment(payRun.startDate).format(
          "MMMM YYYY"
        )}`,
      };

      // Send to Xero integration endpoint
      const xeroResponse = await fetch(
        `/clients/${companyCode}/settings/accounting/xero/sync-payroll`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "CSRF-Token": req.csrfToken(),
          },
          body: JSON.stringify(syncData),
        }
      );

      if (!xeroResponse.ok) {
        const error = await xeroResponse.json();
        throw new Error(error.message || "Failed to sync with Xero");
      }

      // Update pay run with Xero sync status
      payRun.xeroSynced = true;
      payRun.xeroSyncedAt = new Date();
      await payRun.save();

      // Create audit log
      const auditLog = new AuditLog({
        user: req.user._id,
        company: company._id,
        action: "sync_payroll_to_xero",
        details: {
          payRunId: payRun._id,
          syncedAt: new Date(),
          totals,
        },
      });
      await auditLog.save();

      res.json({
        success: true,
        message: "Successfully synced payroll data to Xero",
        syncedAt: new Date(),
      });
    } catch (error) {
      console.error("Error syncing to Xero:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to sync with Xero",
      });
    }
  }
);

// POST route to send payroll data to QuickBooks
router.post(
  "/:companyCode/pay-runs/:payRunId/quickbooks",
  ensureAuthenticated,
  csrfProtection,
  async (req, res) => {
    try {
      const { companyCode, payRunId } = req.params;

      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        throw new Error("Company not found");
      }

      // Find pay run
      const payRun = await PayRun.findById(payRunId)
        .populate("payslips")
        .populate("company");

      if (!payRun) {
        throw new Error("Pay run not found");
      }

      // Check if already synced
      if (payRun.quickbooksSynced) {
        return res.status(400).json({
          success: false,
          message: "This pay run has already been synced to QuickBooks",
        });
      }

      // Check for active QuickBooks integration
      const integration = await Integration.findOne({
        company: company._id,
        provider: "quickbooks",
        type: "accounting",
        status: "active",
      });

      if (!integration) {
        throw new Error("No active QuickBooks integration found");
      }

      // Get account mappings
      const QuickBooksAccountMapping = require("../models/quickbooksAccountMapping");
      const mappings = await QuickBooksAccountMapping.find({
        company: company._id,
      });

      if (!mappings.length) {
        throw new Error("No QuickBooks account mappings configured");
      }

      // Calculate totals
      const totals = {
        basicSalary: payRun.payslips.reduce(
          (sum, p) => sum + (p.basicSalary || 0),
          0
        ),
        paye: payRun.payslips.reduce((sum, p) => sum + (p.PAYE || 0), 0),
        uif: payRun.payslips.reduce((sum, p) => sum + (p.UIF || 0), 0),
        sdl: payRun.payslips.reduce((sum, p) => sum + (p.SDL || 0), 0),
      };

      // Prepare data for QuickBooks sync
      const syncData = {
        payrollId: payRun._id,
        payrollData: {
          basicSalary: totals.basicSalary,
          paye: totals.paye,
          uif: totals.uif,
          sdl: totals.sdl,
          employeeCount: payRun.payslips.length,
          period: `${moment(payRun.startDate).format("MMMM YYYY")} Payroll`,
        },
        description: `Payroll for ${moment(payRun.startDate).format(
          "MMMM YYYY"
        )}`,
      };

      // Send to QuickBooks integration endpoint
      const quickbooksResponse = await fetch(
        `/clients/${companyCode}/settings/accounting/quickbooks/sync-payroll`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "CSRF-Token": req.csrfToken(),
          },
          body: JSON.stringify(syncData),
        }
      );

      if (!quickbooksResponse.ok) {
        const error = await quickbooksResponse.json();
        throw new Error(error.message || "Failed to sync with QuickBooks");
      }

      // Update pay run with QuickBooks sync status
      payRun.quickbooksSynced = true;
      payRun.quickbooksSyncedAt = new Date();
      await payRun.save();

      // Create audit log
      const auditLog = new AuditLog({
        user: req.user._id,
        company: company._id,
        action: "sync_payroll_to_quickbooks",
        details: {
          payRunId: payRun._id,
          syncedAt: new Date(),
          totals,
        },
      });
      await auditLog.save();

      res.json({
        success: true,
        message: "Successfully synced payroll data to QuickBooks",
        syncedAt: new Date(),
      });
    } catch (error) {
      console.error("Error syncing to QuickBooks:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to sync with QuickBooks",
      });
    }
  }
);

// NEW ROUTE: Delete pay run (integrated with PayRunValidationService)
router.delete(
  "/:companyCode/payrun/:payRunId",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { companyCode, payRunId } = req.params;
      const { force, reason } = req.body;


      // Find company
      const company = await Company.findOne({ companyCode });
      if (!company) {
        return res.status(404).json({
          success: false,
          message: "Company not found",
        });
      }

      // Use PayRunValidationService for deletion
      const PayRunValidationService = require("../services/PayRunValidationService");

      const result = await PayRunValidationService.deletePayRun(
        payRunId,
        req.user._id,
        reason || "Deleted via payroll hub",
        force
      );

      if (!result.success) {
        return res.status(400).json(result);
      }

      res.json({
        success: true,
        message: "Pay run deleted successfully",
        payRun: result.payRun,
        warnings: result.warnings,
      });
    } catch (error) {
      console.error("Error deleting pay run:", error);
      res.status(500).json({
        success: false,
        message: "Failed to delete pay run",
        error: error.message,
      });
    }
  }
);

// NEW ROUTE: Get pay run deletion analysis
router.get(
  "/:companyCode/payrun/:payRunId/deletion-analysis",
  ensureAuthenticated,
  async (req, res) => {
    try {
      const { payRunId } = req.params;

      const PayRunValidationService = require("../services/PayRunValidationService");

      const analysis = await PayRunValidationService.getAffectedEntities(payRunId);

      if (analysis.error) {
        return res.status(404).json({
          success: false,
          message: analysis.error,
        });
      }

      res.json({
        success: true,
        analysis: analysis,
      });
    } catch (error) {
      console.error("Error analyzing pay run deletion:", error);
      res.status(500).json({
        success: false,
        message: "Failed to analyze deletion impact",
        error: error.message,
      });
    }
  }
);

// ✅ REMOVED: Recalculation API endpoint (not needed for focused BusinessDate fix)

module.exports = router;
